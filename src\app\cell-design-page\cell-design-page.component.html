<p-toolbar styleClass="mb-4">
    <div class="p-toolbar-group-start gap-2">
        <div *ngIf="metrics">
            <h5 class="mb-1">
                <span i18n="@@cellDesign.topbar.designId">DesignID: {{ design?.designId }}</span> -
                <span i18n="@@cellDesign.topbar.name">Name: {{ design?.name }}</span> -
                <span i18n="@@cellDesign.topbar.releaseStatus"
                    >Freigabestatus: {{ design?.released ? released : notReleased }}</span
                >
                -
                <span i18n="@@cellDesign.topbar.partNumber">Part Nummer: {{ design?.partNumber }}</span>
            </h5>
            <span i18n="@@cellDesign.topbar.capacityAh"
                >Kapazität:
                {{
                    metrics.summary.safeCellCapacityC3 ?? metrics.summary.safeCellCapacityC10 | numberTwoFractionDigits
                }}
                Ah</span
            >,
            <span i18n="@@cellDesign.topbar.energyContentWh"
                >Energienhalt:
                {{ metrics.summary.safeCellEnergyC3 ?? metrics.summary.safeCellEnergyC10 | numberTwoFractionDigits }}
                Wh</span
            >,
            <span i18n="@@cellDesign.topbar.energyDensityVolumentric"
                >Energiedichte Volumetrisch:
                {{
                    metrics.summary.safeCellEnergyDensityVolumetricC3 ??
                        metrics.summary.safeCellEnergyDensityVolumetricC10 | numberTwoFractionDigits
                }}
                Wh/l</span
            >,
            <span i18n="@@cellDesign.topbar.energyDensityGravimetric"
                >Energiedichte Gravimetrisch:
                {{
                    metrics.summary.safeCellEnergyDensityGravimetricC3 ??
                        metrics.summary.safeCellEnergyDensityGravimetricC10 | numberTwoFractionDigits
                }}
                Wh/kg</span
            >,
            <span i18n="@@cellDesign.topbar.packageWeight"
                >Gesamte Zelle: {{ metrics.summary.cellWeightOverall | numberTwoFractionDigits }} g</span
            >
        </div>
    </div>

    <div class="p-toolbar-group-end gap-2">
        <p-menu #exportMenu [model]="exportOptions" [popup]="true"></p-menu>
        <p-button
            (click)="exportMenu.toggle($event)"
            icon="pi pi-file-export"
            i18n-label="@@common.export"
            label="Export"
        ></p-button>

        <p-button
            [disabled]="loading || design?.released || (form.invalid ?? false)"
            i18n-label="@@common.save"
            label="Speichern"
            icon="pi pi-save"
            (onClick)="showSaveDialog()"
        ></p-button>
    </div>
</p-toolbar>

<p-messages *ngIf="error" severity="error">
    <ng-template pTemplate>
        <i class="pi pi-exclamation-circle" style="font-size: 2rem"></i>
        <div class="ml-4" i18n="@@common.genericErrorMessage">
            Ein Fehler ist aufgetreten: {{ error | json | slice : 0 : 600 }}
        </div>
    </ng-template>
</p-messages>

<p-messages *ngIf="form && form.invalid" severity="error" styleClass="mb-4 max-w-max">
    <ng-template pTemplate>
        <i class="pi pi-exclamation-triangle text-4xl"></i>
        <div class="ml-4">
            <p i18n="@@common.validationErrorTitle" class="mt-0 mb-2 text-2xl">Design Validierungsfehler</p>
            <p i18n="@@common.validationErrorMessage" class="m-0">
                Ihr Design enthält Validierungsfehler. Bitte beheben Sie die mit * markierten Fehler, um Metriken zu
                berechnen.
            </p>
        </div>
    </ng-template>
</p-messages>

<com-design-meta-editor
    [design]="design"
    (designChange)="designChanged($event)"
    [(dialogVisible)]="dialogVisible"
    [dialogType]="design?.designId ? MetaEditorTypeEnum.edit : MetaEditorTypeEnum.save"
></com-design-meta-editor>

<form #designForm="ngForm">
    <p-tabView *ngIf="materials && design">
        <p-tabPanel [ngModelGroup]="formGroupNames.chemicalDesign" #chemicalDesignFormGroup="ngModelGroup">
            <ng-template pTemplate="header">
                <span i18n="@@tab.chemicalDesign">Chemie Design</span>
                <span *ngIf="chemicalDesignFormGroup.invalid" class="com-text-error">&nbsp;*</span>
            </ng-template>
            <p-tabView>
                <p-tabPanel [ngModelGroup]="ngGroupNames.cathode" #cathodeFormGroup="ngModelGroup">
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.cathode">Kathode</span>
                        <span *ngIf="cathodeFormGroup.invalid" class="com-text-error">&nbsp;*</span>
                    </ng-template>
                    <com-cathode-editor
                        [materials]="materials.cathode"
                        [design]="design"
                        [metrics]="metrics"
                        [loading]="loading"
                    ></com-cathode-editor>
                </p-tabPanel>
                <p-tabPanel [ngModelGroup]="ngGroupNames.anode" #anodeFormGroup="ngModelGroup">
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.anode">Anode</span>
                        <span *ngIf="anodeFormGroup.invalid" class="com-text-error">&nbsp;*</span>
                    </ng-template>
                    <com-anode-editor
                        [materials]="materials.anode"
                        [design]="design"
                        [metrics]="metrics"
                        [loading]="loading"
                    ></com-anode-editor>
                </p-tabPanel>
                <p-tabPanel [ngModelGroup]="ngGroupNames.balancing" #balancingFormGroup="ngModelGroup">
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.balancing">Balancing</span>
                        <span *ngIf="balancingFormGroup.invalid" class="com-text-error">&nbsp;*</span>
                    </ng-template>
                    <com-balancing-editor
                        #patchesDesignFromMetrics
                        [design]="design"
                        [metrics]="metrics?.balancing"
                        [loading]="loading"
                    ></com-balancing-editor>
                </p-tabPanel>
            </p-tabView>
        </p-tabPanel>
        <p-tabPanel [ngModelGroup]="formGroupNames.cellDesign" #cellDesignFormGroup="ngModelGroup">
            <ng-template pTemplate="header">
                <span i18n="@@tab.cellDesign">Zell Design</span>
                <span *ngIf="cellDesignFormGroup.invalid" class="com-text-error">&nbsp;*</span>
            </ng-template>
            <p-tabView>
                <p-tabPanel [ngModelGroup]="ngGroupNames.materials" #materialsFormGroup="ngModelGroup">
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.materials">Materialien</span>
                        <span *ngIf="materialsFormGroup.invalid" class="com-text-error">&nbsp;*</span>
                    </ng-template>
                    <com-materials-editor
                        [design]="design"
                        [metrics]="metrics?.materials"
                        [loading]="loading"
                        [materials]="materialsById"
                        [inactiveMaterials]="inactiveMaterials"
                    />
                </p-tabPanel>
                <p-tabPanel [ngModelGroup]="ngGroupNames.electrodePair" #electrodePairFormGroup="ngModelGroup">
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.electrodePair">Elektrodenpaar</span>
                        <span *ngIf="electrodePairFormGroup.invalid" class="com-text-error">&nbsp;*</span>
                    </ng-template>
                    <com-electrode-pair-editor
                        [design]="design"
                        [metrics]="metrics?.electrodePair"
                        [loading]="loading"
                    ></com-electrode-pair-editor>
                </p-tabPanel>
                <p-tabPanel [ngModelGroup]="ngGroupNames.cell" #cellFormGroup="ngModelGroup">
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.cell">Zelle</span>
                        <span *ngIf="cellFormGroup.invalid" class="com-text-error">&nbsp;*</span>
                    </ng-template>
                    <com-cell-editor
                        [design]="design"
                        [loading]="loading"
                        [metrics]="metrics?.cell ?? null"
                        [cellFormatOptions]="cellFormatOptions"
                    />
                </p-tabPanel>
                <p-tabPanel [ngModelGroup]="ngGroupNames.electrolyte" #electrolyteFormGroup="ngModelGroup">
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.electrolyte">Elektrolyt</span>
                        <span *ngIf="electrolyteFormGroup.invalid" class="com-text-error">&nbsp;*</span>
                    </ng-template>
                    <com-electrolyte-editor
                        [design]="design"
                        [metrics]="metrics?.electrolyte"
                        [loading]="loading"
                    ></com-electrolyte-editor>
                </p-tabPanel>
            </p-tabView>
        </p-tabPanel>
        <p-tabPanel [ngModelGroup]="formGroupNames.cellSpecs" #cellSpecsFormGroup="ngModelGroup">
            <ng-template pTemplate="header">
                <span i18n="@@tab.cellSpecs">Zell Specs</span>
                <span *ngIf="cellSpecsFormGroup.invalid" class="com-text-error">&nbsp;*</span>
            </ng-template>
            <p-tabView>
                <p-tabPanel [ngModelGroup]="ngGroupNames.energyContent" #energyContentFormGroup="ngModelGroup">
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.energyContent??">Energiegehalt</span>
                        <span *ngIf="energyContentFormGroup.invalid" class="com-text-error">&nbsp;*</span>
                    </ng-template>
                    <com-energy-content-editor
                        [design]="design"
                        [metrics]="metrics?.summary"
                        [loading]="loading"
                    ></com-energy-content-editor>
                </p-tabPanel>
                <p-tabPanel [ngModelGroup]="ngGroupNames.billOfMaterials" #billOfMaterialsFormGroup="ngModelGroup">
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.bom">BOM</span>
                        <span *ngIf="billOfMaterialsFormGroup.invalid" class="com-text-error">&nbsp;*</span>
                    </ng-template>
                    <com-bom-editor
                        [loading]="loading"
                        [metrics]="metrics?.bom?.table"
                        [design]="design"
                    ></com-bom-editor>
                </p-tabPanel>
            </p-tabView>
        </p-tabPanel>
        <p-tabPanel [ngModelGroup]="formGroupNames.designAnalysis" #designAnalysisFormGroup="ngModelGroup">
            <ng-template pTemplate="header">
                <span i18n="@@tab.designAnalysis">Design Analysis</span>
                <span *ngIf="designAnalysisFormGroup.invalid" class="com-text-error">&nbsp;*</span>
            </ng-template>
            <p-tabView>
                <!--
                <p-tabPanel i18n-header="@@tab.thermalProjection" header="Thermische Hochrechnung">
                    Thermische Hochrechnung
                </p-tabPanel>
                -->
                <p-tabPanel [ngModelGroup]="ngGroupNames.socTool" #socToolFormGroup="ngModelGroup">
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.socTool">SOC Tool</span>
                        <span *ngIf="socToolFormGroup.invalid" class="com-text-error">&nbsp;*</span>
                    </ng-template>
                    <com-soc-tool
                        [balancingMetrics]="metrics?.balancing"
                        [cathodeDensity]="design.cathodeCalanderDensity"
                        [anodeDensity]="design.anodeCalanderDensity"
                    ></com-soc-tool>
                </p-tabPanel>
                <p-tabPanel [ngModelGroup]="ngGroupNames.swelling" #swellingFormGroup="ngModelGroup">
                    <ng-template pTemplate="header">
                        <span i18n="@@swelling.swellingCalculation" class="text-center w-12"
                            >Bestimmung des Zellbreathings</span
                        >
                        <span *ngIf="swellingFormGroup.invalid" class="com-text-error">&nbsp;*</span>
                    </ng-template>
                    <com-swelling-calculation-editor
                        [metrics]="metrics"
                        [loading]="loading"
                    ></com-swelling-calculation-editor>
                </p-tabPanel>
                <p-tabPanel>
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.toleranceAnalysis" class="text-center w-12">Toleranzanalyse</span>
                    </ng-template>
                    <com-tolerance-editor [design]="design" [metrics]="metrics"></com-tolerance-editor>
                </p-tabPanel>
            </p-tabView>
        </p-tabPanel>
        <p-tabPanel>
            <ng-template pTemplate="header">
                <span i18n="@@tab.cellOptimization">Zell Optimierung</span>
                <p-progressSpinner *ngIf="isOptimizationPending" styleClass="ml-1 w-1rem h-1rem"></p-progressSpinner>
            </ng-template>
            <p-tabView>
                <p-tabPanel>
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.createOptimization" class="text-center w-10">Erstelle Optimierung</span>
                    </ng-template>
                    <com-optimization-editor [design]="design" [metrics]="metrics"></com-optimization-editor>
                </p-tabPanel>
                <p-tabPanel>
                    <ng-template pTemplate="header">
                        <span i18n="@@tab.parameterOptimization" class="text-center w-10">Parameter Optimierung</span>
                        <p-progressSpinner *ngIf="isOptimizationPending" styleClass="w-2rem h-2rem"></p-progressSpinner>
                    </ng-template>
                    <com-parameter-optimization
                        [design]="design"
                        (designChange)="designChanged($event)"
                        (optimizationPendingStatusChange)="onOptimizationPendingStatusChange($event)"
                    ></com-parameter-optimization>
                </p-tabPanel>
            </p-tabView>
        </p-tabPanel>
    </p-tabView>
</form>
