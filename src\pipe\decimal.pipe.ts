import { DecimalPipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';
import { isNullish } from '@com/utils/object';

@Pipe({
    name: 'numberTwoFractionDigits',
})
export class NumberTwoFractionDigitsPipe implements PipeTransform {
    public constructor(private _decimalPipe: DecimalPipe) {}

    public transform(val: number | string | null | undefined): string | null {
        if (isNullish(val)) {
            return '';
        }

        return this._decimalPipe.transform(val, '1.0-2');
    }
}
