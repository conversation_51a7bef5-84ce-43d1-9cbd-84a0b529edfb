import { InjectionToken, NgModule } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { ToolbarModule } from 'primeng/toolbar';
import { TabViewModule } from 'primeng/tabview';
import { ChartModule } from 'primeng/chart';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { CardModule } from 'primeng/card';
import { InputSwitchModule } from 'primeng/inputswitch';
import { MessagesModule } from 'primeng/messages';
import { MessageModule } from 'primeng/message';
import { TableModule } from 'primeng/table';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ConfirmationService, MessageService, Translation } from 'primeng/api';
import { MenuModule } from 'primeng/menu';
import { CheckboxModule } from 'primeng/checkbox';
import { ToastModule } from 'primeng/toast';
import { InputTextModule } from 'primeng/inputtext';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { PanelModule } from 'primeng/panel';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { CalendarModule } from 'primeng/calendar';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ProgressBarModule } from 'primeng/progressbar';
import { SliderModule } from 'primeng/slider';

import { CustomizePrimeInputDirective } from './customize-prime-input.directive';

export const PRIME_TRANSLATIONS = new InjectionToken<Translation | null>('PRIME_TRANSLATIONS');

@NgModule({
    declarations: [CustomizePrimeInputDirective],
    exports: [
        CustomizePrimeInputDirective,
        ButtonModule,
        CheckboxModule,
        ToolbarModule,
        TabViewModule,
        ChartModule,
        DropdownModule,
        InputNumberModule,
        CardModule,
        InputSwitchModule,
        InputTextModule,
        MessagesModule,
        MessageModule,
        TableModule,
        ProgressSpinnerModule,
        ToastModule,
        MenuModule,
        ConfirmDialogModule,
        DialogModule,
        PanelModule,
        InputTextareaModule,
        CalendarModule,
        AutoCompleteModule,
        ProgressBarModule,
        SliderModule,
    ],
    providers: [MessageService, ConfirmationService],
})
export class PrimeNgModule {}
