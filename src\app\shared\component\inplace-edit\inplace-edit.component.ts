import { Component, ContentChild, EventEmitter, Input, Output, TemplateRef } from '@angular/core';

@Component({
    selector: 'com-inplace-edit',
    templateUrl: 'inplace-edit.component.html',
})
export class InplaceEditComponent<T> {
    @ContentChild('edit')
    public editTemplate: TemplateRef<unknown>;

    @ContentChild('display')
    public displayTemplate: TemplateRef<unknown>;

    @Input()
    public disabled: boolean;

    @Input()
    public showUndo: boolean;

    @Input()
    public value: T | null;

    @Output()
    public valueChange = new EventEmitter<T | null>();

    public isInEdit: boolean;

    public uncomittedValue: T | null;

    public setValue = (value: T | null): void => {
        this.uncomittedValue = value;
    };

    public enterEdit(): void {
        this.isInEdit = true;
        this.uncomittedValue = this.value;
    }

    public exitEdit(): void {
        this.isInEdit = false;
    }

    public acceptValue(value: T | null): void {
        this.isInEdit = false;
        this.valueChange.emit(value);
    }
}
