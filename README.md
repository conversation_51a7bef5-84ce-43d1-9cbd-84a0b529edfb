# CellOMat

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 15.1.3.

## Quick Start

-   Install nodejs version 18.14.2 (optionally install nvm and install nodejs through nvm)
-   Install Angular CLI. (Optionally globally)
-   Run `npm ci` to install dependencies.
-   Run `npm start` for running the app in dev server.
-   Navigate to `https://localhost:4200/`.

## Developer tools

Required plugins:

-   Angular Language Service
-   ESLint
-   Prettier - Code formatter

Recommended:

-   Set prettier as the default formatter for your code editor.
-   Enable format on save for your code editor.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `npm run build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `npm run test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running lint

Run `npm run lint` to execute eslint linting operations. Set to accept 0 warnings. If you need a rule ignored use _eslint-disable_ and specify why this rule is ignored.

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Translations

To extract translations just use `npm run i18n`. The app is currently configured to have a "special" en-x-source locale which is only used so we don't always overwrite the en.json localization file with each extract. This helps to handle EN translations without changing the template source code.

**Note**: You need to use the JSON translation/interpolation format for your translation files.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.

## Code Guide

Coding standards:

-   use **@com** paths for internal imports
-   follow eslint/prettier guidelines
-   example component structure:

```
@Component({
    selector: 'com-test-ordering',
    template: '<div></div>',
})
export abstract class TestOrderingClassComponent {
    public static statPublic: string;
    protected static statProtected: string;
    private static _statPrivate: string;

    @Input()
    public static staticDecorated: string;

    @ViewChild('s')
    public publicDecorated: string;

    @Input()
    public set getSetDecorated(value: string) {
        this._getSetDecorated = value;
    }

    public get getSetDecorated(): string {
        return this._getSetDecorated;
    }

    @Input()
    public set getSetDecorated2(value: string) {
        this._getSetDecorated2 = value;
    }

    public get getSetDecorated2(): string {
        return this._getSetDecorated2;
    }

    public get getSetNonDecorated(): string {
        return this._getSetNonDecorated;
    }

    public set getSetNonDecorated(value: string) {
        this._getSetNonDecorated = value;
    }

    public publicNonDecorated: string;
    protected protected: string;

    private _getSetDecorated: string;
    private _getSetDecorated2: string;
    private _getSetNonDecorated: string;

    private _private: string;

    public abstract get abstractGetSet(): string;
    public abstract set abstractGetSet(value: string);
    public abstract publicAbstractField: string;
    protected abstract protectedAbstractField: string;

    public constructor() {}

    public static staticPublicMethod(): void {}
    protected static staticProtectedMethod(): void {}
    private static staticPrivateMethod(): void {}

    public publicMethod(): void {}
    protected protectedMethod(): void {}
    private privateMethod(): void {}

    public abstract abstractPublicMethod(): void;
    protected abstract abstractProtectedMethod(): void;
}
```
