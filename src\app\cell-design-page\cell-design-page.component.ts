import { <PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON>, <PERSON>mponent, <PERSON><PERSON><PERSON><PERSON>, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { Location } from '@angular/common';
import { NgForm } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { forkJoin, debounceTime, switchMap, tap, map, filter, retry, Subject, takeUntil, merge } from 'rxjs';

import { CellDesignExportService } from '@com/services/cell-design-export.service';
import { CellDesignMetrics, CellDesignMetricsService } from '@com/services/cell-design-metrics.service';
import { CellDesignWithId, CellDesignService } from '@com/services/cell-design.service';
import { CellFormatService, CellFormats } from '@com/services/cell-format.service';
import { Material, InactiveMaterialModel, MaterialsByType, MaterialService } from '@com/services/material.service';
import { delayTick } from '@com/utils/delay-tick';
import { distinctUntilChangedDeepCopy } from '@com/utils/distinct-until-changed-deep-copy';
import { RxHistory } from '@com/utils/rx-history';
import { NG_MODEL_GROUP_NAMES, NG_MODEL_MAIN_SECTION_NAMES } from '@com/app/cell-design-page/const';
import { NotifierService } from '@com/services/notifier.service';
import { MenuItem } from 'primeng/api';
import { MetaEditorTypeEnum } from '@com/app/shared/component/design-meta-editor/design-meta-editor.component';
import { IdLookup, PatchCellDesignFromMetrics } from './types';

@Component({
    selector: 'com-cell-design-page',
    templateUrl: './cell-design-page.component.html',
})
export class CellDesignPageComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('designForm')
    public form: NgForm;

    @ViewChildren('patchesDesignFromMetrics')
    public cellDesignPatchers: QueryList<PatchCellDesignFromMetrics<{}>>;

    public history: RxHistory<CellDesignWithId> = new RxHistory<CellDesignWithId>(100);

    public materials: MaterialsByType | null = null;

    public inactiveMaterials: InactiveMaterialModel[] | null = null;

    public design: CellDesignWithId | null = null;

    public metrics: CellDesignMetrics | null = null;

    public loading: boolean = true;

    public error: unknown | null = null;

    public ngGroupNames = NG_MODEL_GROUP_NAMES;

    public materialsById: IdLookup<Material> = {};

    public dialogVisible = false;

    public released = $localize`:@@common.released:Released`;

    public notReleased = $localize`:@@common.notReleased:Not Released`;

    public cellFormatOptions: CellFormats[] = [];

    public formGroupNames = NG_MODEL_MAIN_SECTION_NAMES;

    public exportOptions: MenuItem[];

    public MetaEditorTypeEnum = MetaEditorTypeEnum;

    public isOptimizationPending: boolean = false;

    private readonly _ngUnsubscribe = new Subject<void>();

    public constructor(
        private _materialService: MaterialService,
        private _cellDesignService: CellDesignService,
        private _cellDesignMetricsService: CellDesignMetricsService,
        private _cellDesignExportService: CellDesignExportService,
        private _route: ActivatedRoute,
        private _location: Location,
        private _cellFormatService: CellFormatService,
        private _notifier: NotifierService
    ) {}

    public ngOnInit(): void {
        this.exportOptions = [
            { label: 'Excel', icon: 'pi pi-file-excel', command: () => this.downloadExcelExport() },
            { label: 'Json', icon: 'pi pi-file', command: () => this.downloadJsonExport() },
        ];

        this._route.params
            .pipe(
                switchMap((params) =>
                    forkJoin({
                        materials: this._materialService.getAllMaterials(),
                        inactiveMaterials: this._materialService.getAllInactiveMaterials(),
                        cellFormats: this._cellFormatService.getAllCellFormats(),
                        design: params['id']
                            ? this._cellDesignService.getSingleCellDesign(params['id'])
                            : this._cellDesignService.getInitialCellDesign(),
                    })
                ),
                takeUntil(this._ngUnsubscribe)
            )

            .subscribe((result) => {
                this.materials = result.materials;
                this.cellFormatOptions = result.cellFormats;
                this.inactiveMaterials = result.inactiveMaterials;
                this.design = result.design as CellDesignWithId;
                this._cellDesignService.setCurrentDesign(this.design);

                const keys = Object.keys(result.materials) as (keyof MaterialsByType)[];
                this.materialsById = {};
                keys.flatMap((type) => result.materials[type]).forEach(
                    (material) => (this.materialsById[material.id] = material)
                );
            });
    }

    public ngAfterViewInit(): void {
        // setup update pipeline for metrics

        const formValueChanges = merge(this.form.control.valueChanges, this._notifier.metricsRefresh$).pipe(
            // wait on next tick to intercept the result of the change detection
            delayTick(),

            // map to design value
            map(() => this.design!),
            filter((design) => design != null && (this.form.valid ?? true)),

            // only continue if the value changed
            distinctUntilChangedDeepCopy(),

            // reduce numer of requests for frequent changes
            debounceTime(500),

            // inject history for undo/redo functionality
            this.history.inject(),

            map((value) => {
                if (value.history) {
                    this.design = value.value;
                }

                return value.value;
            })
        );

        formValueChanges
            .pipe(
                // unset metrics to show loading indicator
                // disable any error notification that might be set
                tap(() => {
                    this.loading = true;
                    this.metrics = null;
                    this.error = null;
                }),

                // project the design change to a backend api call
                switchMap((value) => {
                    this._cellDesignService.setCurrentDesign(value);

                    return this._cellDesignMetricsService.calculateMetrics(value);
                }),

                tap({
                    next: (metrics) => {
                        const patch = {};
                        this.cellDesignPatchers.forEach((patcher) =>
                            Object.assign(patch, patcher.getDesignPatch(metrics))
                        );

                        // Do not emit event to prevent infinite valueChanges loop
                        this.form.control.patchValue(patch, { emitEvent: false });
                    },
                    // when the pipeline fails due to some backend/network issue, set the error and simply resubscribe
                    // to try again on the next value change or manual trigger.
                    error: (err) => {
                        this.loading = false;
                        this.metrics = null;
                        this.error = err;
                    },
                }),
                retry(),
                takeUntil(this._ngUnsubscribe)
            )
            .subscribe((value) => {
                this.loading = false;
                this.metrics = value;
                this.error = null;
            });
    }

    public ngOnDestroy(): void {
        this._ngUnsubscribe.next();
        this._ngUnsubscribe.complete();
    }

    public downloadExcelExport(): void {
        if (this.design) {
            this._cellDesignExportService.exportFile(this.design, 'excel', 'xlsx').subscribe();
        }
    }

    public downloadJsonExport(): void {
        if (this.design) {
            this._cellDesignExportService.exportFile(this.design, 'json', 'json').subscribe();
        }
    }

    public showSaveDialog(): void {
        if (this.form.valid) {
            this.dialogVisible = true;
        }
    }

    public designChanged(design: CellDesignWithId): void {
        this.design = design;
        this._location.replaceState(`/cell-design/${design._id}`);
    }

    public onOptimizationPendingStatusChange(isPending: boolean): void {
        this.isOptimizationPending = isPending;
    }
}
