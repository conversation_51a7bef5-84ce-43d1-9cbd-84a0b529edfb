import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { <PERSON><PERSON>ontainer, NgModelGroup } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { MaterialSummaryTable } from '@com/services/cell-design-metrics.service';
import { BusinessCase, CellDesign, CellDesignService } from '@com/services/cell-design.service';
import { SummaryTableRowCell } from '@com/app/cell-design-page/summary-table/summary-table.component';
import { NotifierService } from '@com/services/notifier.service';

const CONTROL_NAMES = {
    bomDataSetType: 'bom_data_set_type',
    scrapMaterial: 'scrapMaterial',
} as const;

@Component({
    selector: 'com-bom-editor[loading][metrics][design]',
    templateUrl: './bom-editor.component.html',

    // this is important for the change detection to work across components
    // makes this component use the same NgForm as the parent component
    viewProviders: [{ provide: ControlContainer, useExisting: Ng<PERSON><PERSON><PERSON>Group }],
})
export class BomEditorComponent implements OnInit, OnD<PERSON>roy {
    @Input()
    public loading: boolean;

    @Input()
    public design: CellDesign | undefined;

    @Input()
    public metrics: MaterialSummaryTable | undefined;

    public readonly controlNames = CONTROL_NAMES;

    public businessCaseOptions: BusinessCase[] | undefined = undefined;

    private readonly _ngUnsubscribe = new Subject<void>();

    public constructor(private _cellDesignService: CellDesignService, private _notifier: NotifierService) {}

    public ngOnInit(): void {
        this._cellDesignService
            .getBusinessCase()
            .pipe(takeUntil(this._ngUnsubscribe))
            .subscribe((dataSets) => (this.businessCaseOptions = dataSets));
    }

    public ngOnDestroy(): void {
        this._ngUnsubscribe.next();
        this._ngUnsubscribe.complete();
    }

    public onCellValueChange(cell: SummaryTableRowCell): void {
        if (cell.editable && this.design) {
            this.design.bomPrices[cell.editable.id] = +cell.editable.value;
            this._notifier.refreshMetrics();
        }
    }

    public onCellValueReset(cell: SummaryTableRowCell): void {
        if (cell.editable && this.design) {
            delete this.design.bomPrices[cell.editable.id];
            this._notifier.refreshMetrics();
        }
    }

    public hasCellBeenEdited = (cellId: string): boolean => {
        return this.design != null && this.design.bomPrices[cellId] != null;
    };
}
