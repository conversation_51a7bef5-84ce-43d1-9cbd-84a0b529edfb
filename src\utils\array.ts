export function splitByMaxLength<TItem, TResult>(
    items: TItem[],
    maxLength: number,
    projection: (item: TItem, index: number) => TResult
): TResult[][] {
    const result: TResult[][] = [[]];
    let groupCount = 0;
    items.forEach((item: TItem, i: number) => {
        const mapped = projection(item, i);
        if (result[groupCount].length > maxLength - 1) {
            result.push([mapped]);
            groupCount++;
        } else {
            result[groupCount].push(mapped);
        }
    });

    return result;
}

export function groupBy<TItem, T<PERSON><PERSON> extends string | number | symbol>(
    items: readonly TItem[],
    getKey: (item: TItem) => TKey
): Record<TKey, TItem[]> {
    const grouped = items.reduce((result, currentItem) => {
        const groupKey = getKey(currentItem);
        if (!result[groupKey]) {
            result[groupKey] = [];
        }

        result[groupKey].push(currentItem);

        return result;
    }, {} as Record<TKey, TItem[]>);

    return grouped;
}
