import { Component, Input, OnChang<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, SimpleChang<PERSON> } from '@angular/core';
import { MetaEditorTypeEnum } from '@com/app/shared/component/design-meta-editor/design-meta-editor.component';
import { CellDesignMetrics } from '@com/services/cell-design-metrics.service';
import { CellDesignWithId } from '@com/services/cell-design.service';
import {
    ObjectiveVariable,
    OptimizationAlgorithmEnum,
    OptimizationService,
    OptimizationSettings,
    OptimizationVariable,
} from '@com/services/optimization.service';
import { Message } from 'primeng/api';
import { Subject, takeUntil } from 'rxjs';
import { isSwellingEnabled } from '@com/app/cell-design-page/swelling-calculation-editor/swelling-utils';

interface ValidationErrors {
    lowerLimitInvalid?: boolean;
    stepSizeInvalid?: boolean;
    anodeWeightInvalid?: boolean;
    cathodeWeightInvalid?: boolean;
    missingMinValue?: boolean;
    missingMaxValue?: boolean;
    missingStepSizeValue?: boolean;
    objectiveCostInvalid?: boolean;
    objectiveSwellingInvalid?: boolean;
}

const optimizationVariableDefaultValues = { minValue: 10, maxValue: 90, stepSize: 10 };

const iterationsThreshold = 8000;

export enum ParameterVariableEnum {
    anodeWeight = 'anode_weight_0',
    cathodeWeight = 'cathode_weight_0',
}

export enum AnodeMaterialEnum {
    group14SCC = 'group14_scc55',
}

export enum CathodeMaterial1Enum {
    BASF_NCM90_PC_BT98B = 'basf_ncm90_pc_bt98b',
}

export enum CathodeMaterial2Enum {
    MITRACHEM_LMFP = 'mitrachem_LMFP',
    LFP400_IBUTEC = 'lfp400_ibutec',
}

export enum OptimizationObjectiveEnum {
    cost = 'cost',
    cf3BreathingWithCompression = 'cf3_breathing_with_compression',
}

@Component({
    selector: 'com-optimization-editor',
    templateUrl: './optimization-editor.component.html',
    styleUrls: ['./optimization-editor.component.scss'],
})
export class OptimizationEditorComponent implements OnInit, OnDestroy, OnChanges {
    @Input()
    public metrics!: CellDesignMetrics | null;

    @Input()
    public set design(value: CellDesignWithId | undefined) {
        this._design = value;
    }

    public selectedOptimizationSettings: OptimizationSettings | null = null;

    public objectiveVariableSettings: ObjectiveVariable;

    public optimizationVariablesOptions: { name: string; id: string; unit: string }[] = [];

    public optimizationObjectiveOptions: { name: string; id: string }[];

    public stopCriterionOptions: { name: string; id: string }[];

    public optimizationOAlgorithmOptions: { name: string; id: string }[] = [];

    public dialogVisible = false;

    public MetaEditorTypeEnum = MetaEditorTypeEnum;

    public OptimizationAlgorithmEnum = OptimizationAlgorithmEnum;

    public messages: Message[] | undefined;

    public numberOfIterations: number;

    public transitionDuration: string = '300ms';

    public objectivesValidationErrors: ValidationErrors | null = null;

    public optimizationVariablesValidationErrors: Map<string, ValidationErrors | null> = new Map();

    private _optimizationOptions: ObjectiveVariable[];

    private _design: CellDesignWithId | undefined;

    private readonly _ngUnsubscribe = new Subject<void>();

    public constructor(private _optimizationService: OptimizationService) {}

    public get design(): CellDesignWithId | undefined {
        return this._design;
    }

    public ngOnInit(): void {
        this._optimizationService
            .getOptimizationOptions()
            .pipe(takeUntil(this._ngUnsubscribe))
            .subscribe((response) => {
                this._optimizationOptions = response;

                const initialOptimizationSettings: OptimizationSettings = {
                    objective: this._optimizationOptions[0].objective.id,
                    variables: [
                        {
                            ...optimizationVariableDefaultValues,
                            id: this._optimizationOptions[0].variables[0].id,
                            unit: this._optimizationOptions[0].variables[0].unit,
                        },
                    ],
                    algorithm: this._optimizationOptions[0].algorithms[0].id,
                    optimizationTarget: this._optimizationOptions[0].optimizationTarget.id,
                    numInitialPoints: 5,
                    numIterations: 10,
                };

                this.selectedOptimizationSettings = initialOptimizationSettings;
                this.calculateNumberOfIterations();
                this.updateOptionsBasedOnObjective();
                this.optimizationObjectiveOptions = this._optimizationOptions.map((variable) => {
                    return variable.objective;
                });
                this.updateObjectivesValidation();
                this.updateAllVariablesValidation();
            });
    }

    public ngOnChanges(changes: SimpleChanges): void {
        if (changes['design'] || changes['metrics']) {
            this.updateObjectivesValidation();
            this.updateAllVariablesValidation();
        }
    }

    public ngOnDestroy(): void {
        this._ngUnsubscribe.next();
        this._ngUnsubscribe.complete();
    }

    // depending on the objective we select there are different sets
    // of options for the variables, algorithms and stop criterions
    public updateOptionsBasedOnObjective(): void {
        if (!this.selectedOptimizationSettings) {
            return;
        }

        this.updateObjectivesValidation();

        const objectiveVariableSettings = this._optimizationOptions?.find(
            (variable) => variable.objective.id === this.selectedOptimizationSettings?.objective
        );

        //update dropdown options
        if (objectiveVariableSettings) {
            this.objectiveVariableSettings = objectiveVariableSettings;
            this.optimizationVariablesOptions = objectiveVariableSettings.variables;
            this.optimizationOAlgorithmOptions = objectiveVariableSettings.algorithms;
            this.stopCriterionOptions = objectiveVariableSettings.stopCriterions;
            this.selectedOptimizationSettings.variables = [this.selectedOptimizationSettings.variables[0]];
            this.selectedOptimizationSettings.optimizationTarget = objectiveVariableSettings.optimizationTarget.id;
        }
    }

    public onVariableChange(variableId: string): void {
        if (!this.selectedOptimizationSettings) {
            return;
        }

        const selectedVariable = this.selectedOptimizationSettings.variables.find(
            (variable) => variable.id === variableId
        );

        if (!selectedVariable) {
            return;
        }

        const objectiveVariables = this.objectiveVariableSettings?.variables;
        selectedVariable.unit = objectiveVariables?.find((v) => v.id === variableId)?.unit || '';
        this.updateOptimizationVariablesOptions();
        this.updateVariableValidation(selectedVariable);
    }

    public removeOptimizationVariable(index: number): void {
        if (!this.selectedOptimizationSettings) {
            return;
        }

        const removedVariable = this.selectedOptimizationSettings.variables[index];
        if (removedVariable) {
            this.optimizationVariablesValidationErrors.delete(removedVariable.id);
        }

        this.selectedOptimizationSettings.variables.splice(index, 1);
        this.updateOptimizationVariablesOptions();
        this.calculateNumberOfIterations();
    }

    public addOptimizationVariable(): void {
        if (!this.selectedOptimizationSettings) {
            return;
        }

        const availableOption = this.optimizationVariablesOptions.find(
            (option) => !this.selectedOptimizationSettings?.variables.some((variable) => variable.id === option.id)
        );

        if (availableOption) {
            const newVariable = {
                id: availableOption.id,
                unit: availableOption.unit,
            };

            this.selectedOptimizationSettings.variables.push(newVariable);
            this.updateOptimizationVariablesOptions();
            this.calculateNumberOfIterations();
            this.updateVariableValidation(newVariable);
        }
    }

    public updateVariableValidation(variable: OptimizationVariable): void {
        const errors = this.validateOptimizationVariable(variable);
        this.optimizationVariablesValidationErrors.set(variable.id, errors);
    }

    public updateAllVariablesValidation(): void {
        if (!this.selectedOptimizationSettings) {
            return;
        }

        this.optimizationVariablesValidationErrors.clear();

        for (const variable of this.selectedOptimizationSettings.variables) {
            this.updateVariableValidation(variable);
        }
    }

    public updateObjectivesValidation(): void {
        this.objectivesValidationErrors = this.validateObjectives();
    }

    public areOptimizationSettingsInvalid(): boolean {
        return this.areOptimizationVariablesInvalid() || this.objectivesValidationErrors !== null;
    }

    public onStartOptimizationClick(): void {
        if (this.areOptimizationSettingsInvalid()) {
            return;
        }

        this.showSaveDialog();
    }

    public calculateNumberOfIterations(): void {
        let totalIterations = 1;
        let newMessage = null;

        if (!this.selectedOptimizationSettings) {
            return;
        }

        if (this.selectedOptimizationSettings.algorithm === OptimizationAlgorithmEnum.brute) {
            for (const variable of this.selectedOptimizationSettings.variables) {
                const range = (variable.maxValue ?? 0) - (variable.minValue ?? 0);
                const iterations = Math.floor(range / (variable.stepSize || 1)) + 1;

                totalIterations *= iterations;
            }

            this.numberOfIterations = totalIterations;

            if (this.numberOfIterations >= iterationsThreshold) {
                newMessage = {
                    severity: 'warn',
                    detail: $localize`:@@createOptimization.warningMessageGrid:Diese Optimierungskonfiguration kann länger dauern. Reduzieren Sie den Bereich der Variablenlimits (unteres und oberes Limit) oder erhöhen Sie die Schrittgröße, um den Vorgang zu beschleunigen.`,
                };
            } else {
                newMessage = null;
            }
        } else if (this.selectedOptimizationSettings.algorithm === OptimizationAlgorithmEnum.bayesian) {
            this.numberOfIterations =
                (this.selectedOptimizationSettings?.numIterations ?? 0) +
                (this.selectedOptimizationSettings?.numInitialPoints ?? 0);

            if (this.numberOfIterations >= iterationsThreshold) {
                newMessage = {
                    severity: 'warn',
                    detail: $localize`:@@createOptimization.warningMessageBayesian:Diese Optimierungskonfiguration kann länger dauern. Um den Prozess zu beschleunigen, reduzieren Sie die Anzahl der Anfangspunkte oder verringern Sie die Anzahl der Iterationen.`,
                };
            } else {
                newMessage = null;
            }
        }

        if (JSON.stringify(this.messages) !== JSON.stringify([newMessage])) {
            this.messages = newMessage ? [newMessage] : [];
        }
    }

    private validateOptimizationVariable(variable: OptimizationVariable): ValidationErrors | null {
        if (!this.design) {
            return null;
        }

        const errors: ValidationErrors = {};

        if (variable.id === ParameterVariableEnum.cathodeWeight) {
            const isCathodeMaterial1Invalid = !this.isValidCathodeMaterial(
                this.design.cathodeMaterials[0].materialId,
                Object.values(CathodeMaterial1Enum) as string[]
            );

            const isCathodeMaterial2Invalid = !this.isValidCathodeMaterial(
                this.design.cathodeMaterials[1]?.materialId,
                Object.values(CathodeMaterial2Enum) as string[]
            );

            if (isCathodeMaterial1Invalid || isCathodeMaterial2Invalid) {
                errors.cathodeWeightInvalid = true;
            }
        }

        if (
            variable.id === ParameterVariableEnum.anodeWeight &&
            (this.design.anodeMaterials.length <= 1 ||
                this.design.anodeMaterials[0].materialId !== AnodeMaterialEnum.group14SCC)
        ) {
            errors.anodeWeightInvalid = true;
        }

        if (!variable.minValue) {
            errors.missingMinValue = true;
        }

        if (!variable.maxValue) {
            errors.missingMaxValue = true;
        }

        if (!variable.stepSize && this.selectedOptimizationSettings?.algorithm !== OptimizationAlgorithmEnum.bayesian) {
            errors.missingStepSizeValue = true;
        }

        if (variable.minValue && variable.maxValue && variable.minValue >= variable.maxValue) {
            errors.lowerLimitInvalid = true;
        }

        if (
            variable.stepSize &&
            variable.stepSize > Math.abs(variable.maxValue! - variable.minValue!) &&
            this.selectedOptimizationSettings?.algorithm !== OptimizationAlgorithmEnum.bayesian
        ) {
            errors.stepSizeInvalid = true;
        }

        return Object.keys(errors).length ? errors : null;
    }

    private updateOptimizationVariablesOptions(): void {
        if (!this.selectedOptimizationSettings) {
            return;
        }

        const selectedIds = this.selectedOptimizationSettings.variables.map((variable) => variable.id);
        this.optimizationVariablesOptions = this.optimizationVariablesOptions.map((option) => ({
            ...option,
            disabled: selectedIds.includes(option.id),
        }));
    }

    private validateObjectives(): ValidationErrors | null {
        const errors: ValidationErrors = {};
        const lastRow = this.metrics?.bom.table.rows?.[this.metrics.bom.table.rows.length - 1];

        if (
            !lastRow?.cells?.[3]?.value &&
            this.selectedOptimizationSettings?.objective === OptimizationObjectiveEnum.cost
        ) {
            errors.objectiveCostInvalid = true;
        }

        if (this.selectedOptimizationSettings?.objective === OptimizationObjectiveEnum.cf3BreathingWithCompression) {
            const result = isSwellingEnabled(this.design || null);

            if (!result.enabled) {
                errors.objectiveSwellingInvalid = true;
            }
        }

        return Object.keys(errors).length ? errors : null;
    }

    private areOptimizationVariablesInvalid(): boolean {
        if (!this.design || !this.selectedOptimizationSettings) {
            return true;
        }
        return this.selectedOptimizationSettings.variables.some(
            (v) => this.optimizationVariablesValidationErrors.get(v.id) !== null
        );
    }

    private showSaveDialog(): void {
        this.dialogVisible = true;
    }

    private isValidCathodeMaterial(materialId: string, allowedValues: string[]): boolean {
        return allowedValues.includes(materialId);
    }
}
