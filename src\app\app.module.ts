import { APP_INITIALIZER, LOCALE_ID, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';

import { environment } from '@com/environments/environment';
import { ConfigService } from '@com/services/config.service';
import { getLocale } from '@com/utils/i18n';

import { MsalRedirectComponent } from '@azure/msal-angular';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { StartPageComponent } from './start-page/start-page.component';
import { PrimeNgModule } from './prime-ng';
import { CellDesignModule } from './cell-design-page/cell-design.module';
import { AuthModule } from './auth.module';
import { SharedModule } from './shared/shared.module';
import { DesignBrowserComponent } from './start-page/design-browser/design-browser.component';
import { DesignMenuComponent } from './start-page/design-browser/design-menu.component';
import { ErrorInterceptor } from './shared/error-interceptor';

@NgModule({
    declarations: [AppComponent, StartPageComponent, DesignBrowserComponent, DesignMenuComponent],
    imports: [
        SharedModule,
        BrowserModule,
        BrowserAnimationsModule,
        HttpClientModule,
        AppRoutingModule,
        FormsModule,
        PrimeNgModule,
        CellDesignModule,
        AuthModule,
    ],
    providers: [
        {
            provide: APP_INITIALIZER,
            useFactory:
                (service: ConfigService): (() => void) =>
                () =>
                    service.load(environment),
            deps: [ConfigService],
            multi: true,
        },
        {
            provide: LOCALE_ID,
            useFactory: getLocale,
        },
        {
            provide: HTTP_INTERCEPTORS,
            useClass: ErrorInterceptor,
            multi: true,
        },
    ],
    bootstrap: [AppComponent, MsalRedirectComponent],
})
export class AppModule {}
