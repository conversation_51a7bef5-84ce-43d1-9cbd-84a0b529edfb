import { Component, Input, OnInit } from '@angular/core';
import { ControlContainer, NgModelGroup } from '@angular/forms';
import { CellDesignMetrics } from '@com/services/cell-design-metrics.service';
import { CellDesign, CellDesignService } from '@com/services/cell-design.service';
import { isSwellingEnabled } from './swelling-utils';

const CONTROL_NAMES = {
    swellingDuringFormation: 'swelling_during_formation',
    compressibilityOverPressureRange: 'compressibility_over_pressure_range',
    cathodeArealCapacity: 'cathode_areal_capacity',
    thicknessAluminiumFoil: 'thickness_aluminium_foil',
    thicknessCopperFoil: 'thickness_copper_foil',
    thicknessSeparator: 'thickness_separator',
} as const;

@Component({
    selector: 'com-swelling-calculation-editor',
    templateUrl: './swelling-calculation-editor.component.html',
    viewProviders: [{ provide: ControlContainer, useExisting: NgModelGroup }],
})
export class SwellingCalculationEditorComponent implements OnInit {
    @Input()
    public metrics!: CellDesignMetrics | null;

    @Input()
    public loading: boolean;

    public design: CellDesign | null;

    public readonly controlNames = CONTROL_NAMES;

    public readonly maxDigits = 4;

    public compressionFromExternalForce: number;

    public isRigidSetup: boolean = false;

    public swellingCalculationEnabled: boolean = false;
    public messageForEnabling: string = '';

    public constructor(private _cellDesignService: CellDesignService) {}

    public ngOnInit(): void {
        this._cellDesignService.getCurrentDesign().subscribe((value) => {
            this.design = value;
            this.updateSwellingCalculationStatus();
        });
    }

    private updateSwellingCalculationStatus(): void {
        const result = isSwellingEnabled(this.design);
        this.swellingCalculationEnabled = result.enabled;
        this.messageForEnabling = result.message;
    }
}
