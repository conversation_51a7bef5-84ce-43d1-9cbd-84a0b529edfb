<ng-container *ngIf="items.length > 1">
    <p-menu #menu [popup]="true" [model]="items" appendTo="body" styleClass="fit-content"></p-menu>
    <button
        pButton
        type="button"
        class="p-button-secondary"
        icon="pi pi-ellipsis-v"
        (click)="menu.toggle($event)"
    ></button>
</ng-container>

<p-button
    *ngIf="items.length === 1"
    [label]="items[0].label!"
    [icon]="items[0].icon!"
    iconPos="left"
    (onClick)="items[0].command?.()"
></p-button>

<p-confirmDialog
    class="com-dialog-message-overflow-ellipsis"
    i18n-header="@@common.confirmation"
    header="Confirmation"
    icon="pi pi-exclamation-triangle"
    acceptButtonStyleClass=""
    rejectButtonStyleClass="p-button-secondary"
></p-confirmDialog>

<com-design-meta-editor
    [design]="design"
    (designChange)="designChanged()"
    [(dialogVisible)]="modalOpen"
    [dialogType]="MetaEditorTypeEnum.edit"
></com-design-meta-editor>
