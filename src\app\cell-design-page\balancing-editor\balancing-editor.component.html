<div *ngIf="design" class="grid align-items-stretch">
    <div class="col-12 lg:col-6">
        <p-card>
            <h5 i18n="@@tab.balancing">Balancing</h5>
            <div class="field grid">
                <label
                    [for]="controlNames.npRatioFirst"
                    class="col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@balancing.npRatioFirst.input.label"
                >
                    N/P Verhältnis Formierung
                </label>
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        [name]="controlNames.npRatioFirst"
                        [inputId]="controlNames.npRatioFirst"
                        [(ngModel)]="design.balancingNpRatioFirst"
                        mode="decimal"
                        [maxFractionDigits]="maxDigits"
                        [min]="0.7"
                        [max]="100"
                        [required]="true"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0">
                    <strong i18n="@@balancing.npRatioRev.label">N/P Verhältnis Reversibel</strong>
                </label>
                <div class="col-12 xl:col-9 p-fluid">
                    <strong *ngIf="metrics && metrics.npRatioRev" class="ml-1">
                        {{ metrics.npRatioRev | numberTwoFractionDigits }}
                    </strong>
                </div>
            </div>

            <hr />
            <h5 i18n="@@common.voltage">Spannung</h5>
            <div class="field grid">
                <label
                    [for]="controlNames.uMin"
                    class="col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@balancing.uMin.input.label"
                >
                    Umin
                </label>
                <!-- TODO: Verify correct maxFractionDigits - 'self-correcting' behavior on Umin/Umax depends on more fraction digits,
                but PrimeNG always rounds to the maxFraction digits, which is used for display as well as rounding and is at most 20 -->
                <!-- TODO: Verify min/max -->
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        comCustomizeInput
                        #uMinInput
                        [name]="controlNames.uMin"
                        [inputId]="controlNames.uMin"
                        [(ngModel)]="design.balancingUMin"
                        [max]="uMaxInput.value"
                        mode="decimal"
                        suffix=" V"
                        [maxFractionDigits]="maxDigits"
                        [required]="true"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label
                    [for]="controlNames.uMax"
                    class="col-12 mb-2 xl:col-3 xl:mb-0"
                    i18n="@@balancing.uMax.input.label"
                >
                    Umax
                </label>
                <!-- TODO: Verify correct maxFractionDigits & min/max -->
                <div class="col-12 xl:col-9 p-fluid">
                    <p-inputNumber
                        #uMaxInput
                        comCustomizeInput
                        [name]="controlNames.uMax"
                        [inputId]="controlNames.uMax"
                        [(ngModel)]="design.balancingUMax"
                        [min]="uMinInput.value"
                        mode="decimal"
                        suffix=" V"
                        [maxFractionDigits]="maxDigits"
                        [required]="true"
                    ></p-inputNumber>
                </div>
            </div>

            <div class="field grid">
                <label class="col-12 mb-2 xl:col-3 xl:mb-0">
                    <strong i18n="@@balancing.hysteresis.label">Hysterese</strong>
                </label>
                <div class="col-12 xl:col-9 p-fluid">
                    <strong *ngIf="metrics && metrics.hysteresis" class="ml-1" i18n="@@common.milliVolts">
                        {{ metrics.hysteresis | number : "1.0-0" }}&nbsp;mV
                    </strong>
                </div>
            </div>

            <hr />
        </p-card>

        <p-card>
            <div *ngIf="loading" class="flex justify-content-center">
                <p-progressSpinner />
            </div>

            <com-summary-table
                *ngIf="metrics"
                [headers]="metrics.summary.headers"
                [rows]="metrics.summary.rows"
            ></com-summary-table>
        </p-card>
    </div>

    <div class="col-12 lg:col-6">
        <com-material-chart [data]="metrics?.chart" [options]="options" [loading]="loading" />
    </div>
</div>
