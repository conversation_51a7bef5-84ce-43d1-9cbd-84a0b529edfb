import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { groupBy } from '@com/utils/array';
import { map, Observable } from 'rxjs';
import { ConfigService } from './config.service';

export interface Material {
    id: string;
    name: string;
    type: keyof MaterialsByType;
    blendable: boolean;
    versions: MaterialVersionModel[];
}

export interface MaterialVersionModel {
    id: string;
    date: string;
    description: string;
}

export interface MaterialsByType {
    anode: Material[];
    cathode: Material[];
}

export interface InactiveMaterialModel {
    id: string;
    name: string;
    type: MaterialType;
    density: number;
    isAnode: boolean;
    isCathode: boolean;
}

export enum MaterialType {
    binder = 'MaterialType.BINDER',
    conductiveAdditive = 'MaterialType.CONDUCTIVE_ADDITIVE',
    electrolyte = 'MaterialType.ELECTROLYTE',
    currentCollector = 'MaterialType.CURRENT_COLLECTOR',
    separator = 'MaterialType.SEPARATOR',
    housting = 'MaterialType.HOUSING',
    tab = 'MaterialType.TAB',
    electrode = 'MaterialType.ELECTRODE',
}

@Injectable({
    providedIn: 'root',
})
export class MaterialService {
    public constructor(private _config: ConfigService, private _http: HttpClient) {}

    public getAllMaterials(): Observable<MaterialsByType> {
        return this._http
            .get<Material[]>(`${this._config.data.baseUrl}/api/material/`)
            .pipe(map((materials) => groupBy(materials, (material) => material.type)));
    }

    public getAllInactiveMaterials(): Observable<InactiveMaterialModel[]> {
        return this._http.get<InactiveMaterialModel[]>(`${this._config.data.baseUrl}/api/inactive-material/`);
    }
}
