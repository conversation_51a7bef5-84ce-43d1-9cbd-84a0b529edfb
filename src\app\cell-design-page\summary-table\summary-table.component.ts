import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
    MaterialSummaryTableRow,
    SummaryCellAcceptedTypes,
    TableRowCellEditableApiData,
} from '@com/services/cell-design-metrics.service';

@Component({
    selector: 'com-summary-table',
    templateUrl: './summary-table.component.html',
    styleUrls: ['summary-table.component.scss'],
})
export class SummaryTableComponent {
    @Output()
    public cellValueChange = new EventEmitter<SummaryTableRowCell>();
    @Output()
    public cellValueReset = new EventEmitter<SummaryTableRowCell>();

    @Input() public headers: string[];
    @Input() public hasCellBeenEdited?: (cellId: string) => boolean;
    @Input() public highlightedColumns: number[] = [];
    @Input()
    public set rows(value: MaterialSummaryTableRow[]) {
        this.mapSummaryRows(value);
    }

    public mappedRows: MaterialSummaryTableRow[] = [];

    public isNaN(value: number): boolean {
        return isNaN(value);
    }

    public onCellValueChange(cell: SummaryTableRowCell, value: number): void {
        cell.value = value;
        cell.editable!.value = value;
        this.cellValueChange.emit(cell);
    }

    public onCellValueReset(cell: SummaryTableRowCell): void {
        this.cellValueReset.emit(cell);
    }

    private mapSummaryRows(rows: MaterialSummaryTableRow[]): void {
        this.mappedRows = [];

        rows.forEach((row) => {
            const newCells: SummaryTableRowCell[] = [];

            row.cells.forEach((cell) => {
                if (cell.editable) {
                    newCells.push({
                        unit: cell.unit,
                        value: cell.value,
                        editable: {
                            id: cell.editable.id,
                            min: cell.editable.min,
                            max: cell.editable.max,
                            isInEdit: false,
                            hasBeenEdited:
                                this.hasCellBeenEdited != null ? this.hasCellBeenEdited(cell.editable.id) : false,
                            value: cell.value,
                        },
                    });
                } else {
                    newCells.push({ unit: cell.unit, value: cell.value });
                }
            });

            const mappedRow: MaterialSummaryTableRow = {
                cells: newCells,
                ...('isPrimary' in row && { isPrimary: row.isPrimary }),
            };

            this.mappedRows.push(mappedRow);
        });
    }
}

export interface SummaryTableRow {
    cells: SummaryTableRowCell[];
}

export interface SummaryTableRowCell {
    unit?: string | null;
    value: SummaryCellAcceptedTypes;
    editable?: SummaryTableRowCellEditableData;
    isHighlighted?: boolean;
}

export interface SummaryTableRowCellEditableData extends TableRowCellEditableApiData {
    isInEdit: boolean;
    hasBeenEdited: boolean;
    value: SummaryCellAcceptedTypes;
}
