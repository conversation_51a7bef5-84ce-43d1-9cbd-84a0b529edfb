import { Component, ElementRef, HostListener, Inject, OnInit, Renderer2, ViewChild } from '@angular/core';
import { Chart } from 'chart.js';
import { MenuItem, PrimeNGConfig, Translation } from 'primeng/api';
import annotationPlugin from 'chartjs-plugin-annotation';
import { getLanguageMenuItems } from '@com/utils/i18n';
import { Menu } from 'primeng/menu';
import { PRIME_TRANSLATIONS, ToastPositionEnum } from './prime-ng';

const topbarMenuMobileClass = 'layout-topbar-menu-mobile-active';
const mobileBreakpoint = 991; // value should be equal to _topbar.scss media query breakpoint
const resizeDebounceDelay = 200;

@Component({
    selector: 'com-root',
    templateUrl: './app.component.html',
})
export class AppComponent implements OnInit {
    @ViewChild('topbarmenu') public topbarmenu!: ElementRef;
    @ViewChild('topbarmenubutton') public topbarmenubutton!: ElementRef;
    @ViewChild('menu') public pMenu!: Menu;

    public toastKey = ToastPositionEnum.topCenter;

    public toastPosition = ToastPositionEnum.topCenter;

    public items: MenuItem[] = [];

    private _resizeTimeout: ReturnType<typeof setTimeout>; // Timeout reference for debouncing

    public constructor(
        private _primeNg: PrimeNGConfig,
        @Inject(PRIME_TRANSLATIONS) private _primeTranslations: Translation | null,
        private _renderer: Renderer2
    ) {}

    public ngOnInit(): void {
        if (this._primeTranslations) {
            this._primeNg.setTranslation(this._primeTranslations);
            this.items = getLanguageMenuItems();
        }

        // TODO: Colors from CSS?
        const textColor = 'rgba(255, 255, 255, 0.87)';
        Chart.register(annotationPlugin);
        Chart.defaults.color = textColor;
        Chart.defaults.borderColor = '#747a82';
    }

    public toggleMenu(): void {
        const hasClass = this.topbarmenu.nativeElement.classList.contains(topbarMenuMobileClass);
        if (hasClass) {
            this._renderer.removeClass(this.topbarmenu.nativeElement, topbarMenuMobileClass);
        } else {
            this._renderer.addClass(this.topbarmenu.nativeElement, topbarMenuMobileClass);
        }
    }

    // if the click was outside the button and menu close the top menu
    @HostListener('document:click', ['$event'])
    public clickOutside(event: MouseEvent): void {
        const targetElement = event.target as HTMLElement;

        if (
            !this.topbarmenubutton.nativeElement.contains(targetElement) &&
            !this.topbarmenu.nativeElement.contains(targetElement)
        ) {
            this._renderer.removeClass(this.topbarmenu.nativeElement, topbarMenuMobileClass);
            this.pMenu.hide(); // needed to add this otherwise the items are not closing when clicked on Languages
        }
    }

    // if window's width is greater than 991px, close the top menu
    @HostListener('window:resize', ['$event'])
    public onResize(): void {
        clearTimeout(this._resizeTimeout);

        this._resizeTimeout = setTimeout(() => {
            const windowWidth = window.innerWidth;

            if (windowWidth > mobileBreakpoint) {
                this._renderer.removeClass(this.topbarmenu.nativeElement, topbarMenuMobileClass);
                this.pMenu.hide();
            }
        }, resizeDebounceDelay);
    }
}
