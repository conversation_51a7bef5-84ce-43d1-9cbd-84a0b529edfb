<div *ngIf="loading; else energyContentEditor" class="flex justify-content-center">
    <p-progressSpinner />
</div>

<ng-template #energyContentEditor>
    <div *ngIf="design" class="grid align-items-stretch">
        <ng-template #dividerSmall><hr class="col-span-4 mobile-only" /></ng-template>

        <div class="col-12 lg:col-6">
            <p-panel i18n-header="@@common.cellDimensions" header="Zelldimensionen">
                <div class="css-grid">
                    <ng-container>
                        <label class="col-span-2 shrink-col" i18n="@@energyContent.cellWeightOverall.label">
                            Gewicht gesamte Zelle
                        </label>
                        <div class="col-span-2 shrink-col" i18n="@@common.gram">
                            {{ metrics?.cellWeightOverall | numberTwoFractionDigits }} g
                        </div>
                    </ng-container>

                    <ng-container>
                        <label class="col-span-2 shrink-col" i18n="@@energyContent.cellVolumeOverall.label">
                            Volume gesamte Zelle
                        </label>
                        <div class="col-span-2 shrink-col" i18n="@@common.litre">
                            {{ metrics?.cellVolumeOverall | numberTwoFractionDigits }} l
                        </div>
                    </ng-container>

                    <ng-container>
                        <label class="col-span-2 shrink-col" i18n="@@energyContent.priceCell.label">
                            Materialpreis pro Celle
                        </label>
                        <div class="col-span-2 shrink-col" i18n="@@common.euro">
                            {{ metrics?.priceCell | numberTwoFractionDigits }} €
                        </div>
                    </ng-container>
                </div>
            </p-panel>
        </div>

        <div class="col-12 lg:col-6">
            <p-panel i18n-header="@@energyContent.capacityAndEnergyContent" header="Kapazität und Energiegehalt">
                <div class="field grid">
                    <label class="break-word col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@common.safety">Sicherheit</label>
                    <div class="col-12 xl:col-9 p-fluid">
                        <p-inputNumber
                            comCustomizeInput
                            [name]="controlNames.safety"
                            [inputId]="controlNames.safety"
                            [(ngModel)]="design.safety"
                            [maxFractionDigits]="4"
                            mode="decimal"
                            [min]="0.0001"
                            [max]="1"
                            [required]="true"
                        ></p-inputNumber>
                    </div>
                </div>
                <div class="css-grid grid-3-col colored shrink-row">
                    <ng-container>
                        <div></div>
                        <label class="break-word" i18n="@@common.cRate10Symbol">C/10</label>
                        <label class="break-word" i18n="@@common.cRate3Symbol">C/3</label>
                    </ng-container>
                    <ng-container *ngTemplateOutlet="dividerSmall" />

                    <ng-container *ngFor="let item of capacityAndEnergyContentData">
                        <label class="break-word">{{ item.label }}</label>
                        <strong>{{ item.c10Value }}</strong>
                        <strong>{{ item.c3Value }}</strong>
                        <ng-container *ngTemplateOutlet="dividerSmall" />
                    </ng-container>
                </div>
            </p-panel>

            <p-panel class="mt-3 block" i18n-header="@@common.packDesign" header="Pack Design">
                <div class="css-grid">
                    <div class="field grid col-span-2">
                        <label
                            class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@energyContent.parallelCellsCount.label"
                            >Zellen parallel</label
                        >
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-inputNumber
                                comCustomizeInput
                                [name]="controlNames.parallelCellsCount"
                                [inputId]="controlNames.parallelCellsCount"
                                [(ngModel)]="parallelCellsCount"
                                mode="decimal"
                                [min]="packDesignMinValue"
                                [max]="packDesignMaxValue"
                                [required]="true"
                            ></p-inputNumber>
                        </div>
                    </div>

                    <div class="field grid col-span-2">
                        <label
                            class="break-word col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@energyContent.serialCellsCount.label"
                            >Zellen seriell</label
                        >
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-inputNumber
                                comCustomizeInput
                                [name]="controlNames.serialCellsCount"
                                [inputId]="controlNames.serialCellsCount"
                                [(ngModel)]="serialCellsCount"
                                mode="decimal"
                                [min]="packDesignMinValue"
                                [max]="packDesignMaxValue"
                                [required]="true"
                            ></p-inputNumber>
                        </div>
                    </div>

                    <div class="field grid col-span-2">
                        <label class="break-word col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@common.moduleCount"
                            >Anzahl Module</label
                        >
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-inputNumber
                                comCustomizeInput
                                [name]="controlNames.moduleCount"
                                [inputId]="controlNames.moduleCount"
                                [(ngModel)]="moduleCount"
                                mode="decimal"
                                [min]="packDesignMinValue"
                                [required]="true"
                            ></p-inputNumber>
                        </div>
                    </div>

                    <div class="field grid col-span-2">
                        <label class="break-word col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@energyContent.cellsPerModule.label"
                            >Zellen pro Modul</label
                        >
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-inputNumber
                                comCustomizeInput
                                [name]="controlNames.cellsPerModule"
                                [inputId]="controlNames.cellsPerModule"
                                [(ngModel)]="cellsPerModule"
                                mode="decimal"
                                [min]="packDesignMinValue"
                                [required]="true"
                            ></p-inputNumber>
                        </div>
                    </div>

                    <div class="field grid col-span-2">
                        <!-- spacer -->
                    </div>

                    <ng-container>
                        <label class="break-word xl:mb-0" i18n="@@common.cellsTotal">Zellen gesamt</label>
                        <div>
                            {{ metrics?.cellCount | numberTwoFractionDigits }}
                        </div>
                    </ng-container>

                    <ng-container>
                        <label class="col-span-2 shrink-col xl:mb-0" i18n="@@common.nominalVoltage"
                            >Nominelle Spannung</label
                        >
                        <div class="col-span-2 shrink-col" i18n="@@common.volt">
                            {{ metrics?.packNominalVoltage | numberTwoFractionDigits }} V
                        </div>
                    </ng-container>

                    <ng-container>
                        <label class="col-span-2 shrink-col xl:mb-0" i18n="@@common.capacity">Kapazität</label>
                        <div class="col-span-2 shrink-col" i18n="@@common.amperHour">
                            {{ metrics?.safePackCapacity | numberTwoFractionDigits }} Ah
                        </div>
                    </ng-container>

                    <ng-container>
                        <label class="col-span-2 shrink-col xl:mb-0" i18n="@@common.energyHold">Energienhalt</label>
                        <div class="col-span-2 shrink-col" i18n="@@common.kiloWatHour">
                            {{ metrics?.safePackEnergy | numberTwoFractionDigits }} kWh
                        </div>
                    </ng-container>

                    <ng-container>
                        <label class="col-span-2 shrink-col xl:mb-0" i18n="@@common.packingWeight">Packgewicht</label>
                        <div class="col-span-2 shrink-col" i18n="@@common.kilogram">
                            {{ metrics?.packWeight | numberTwoFractionDigits }} kg
                        </div>
                    </ng-container>

                    <ng-container>
                        <label class="col-span-2 shrink-col xl:mb-0" i18n="@@common.price"> Preis </label>
                        <div class="col-span-2 shrink-col" i18n="@@common.euro">
                            {{ metrics?.packPrice | numberTwoFractionDigits }} €
                        </div>
                    </ng-container>
                </div>
            </p-panel>
        </div>
    </div>
</ng-template>
