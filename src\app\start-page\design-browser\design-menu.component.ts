import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { ToastPositionEnum, ToastSeverityEnum } from '@com/app/prime-ng';
import { CellDesignWithId, CellDesignService } from '@com/services/cell-design.service';
import { AuthService, UserRolesEnum } from '@com/services/auth.service';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { Subject, takeUntil } from 'rxjs';
import { MetaEditorTypeEnum } from '@com/app/shared/component/design-meta-editor/design-meta-editor.component';

@Component({
    selector: 'com-design-menu',
    templateUrl: 'design-menu.component.html',
    providers: [ConfirmationService, MessageService],
})
export class DesignMenuComponent implements OnInit, OnDestroy {
    @Input()
    public design: CellDesignWithId;

    @Output()
    public designChange = new EventEmitter<void>();

    public items: MenuItem[] = [];

    public modalOpen: boolean;
    public MetaEditorTypeEnum = MetaEditorTypeEnum;

    private readonly _ngUnsubscribe: Subject<void> = new Subject();

    public constructor(
        private _router: Router,
        private _confirmationService: ConfirmationService,
        private _messageService: MessageService,
        private _designService: CellDesignService,
        private _authService: AuthService
    ) {}

    public ngOnInit(): void {
        this.items = [
            {
                label: $localize`:@@common.open:Open`,
                icon: 'pi pi-fw pi-external-link',
                command: this.goToDesign,
            },
        ];

        if (this._authService.hasAnyRole(UserRolesEnum.release, UserRolesEnum.admin)) {
            this.items.push({
                label: $localize`:@@common.editOrRelease:Edit Metadata/Release`,
                icon: 'pi pi-fw pi-pencil',
                command: this.openModal,
            });
        } else {
            this.items.push({
                label: $localize`:@@common.editMetadata:Edit Metadata`,
                icon: 'pi pi-fw pi-pencil',
                command: this.openModal,
            });
        }

        if (this._authService.hasRole(UserRolesEnum.admin)) {
            this.items.push({
                label: $localize`:@@common.delete:Delete`,
                icon: 'pi pi-fw pi-trash',
                command: this.deleteDesign,
            });
        }
    }

    public ngOnDestroy(): void {
        this._ngUnsubscribe.next();
        this._ngUnsubscribe.complete();
    }

    public designChanged(): void {
        this.designChange.emit();
    }

    public executeFirstCommand(): void {
        if (this.items.length > 0 && this.items[0].command) {
            // Create a mock MenuItemCommandEvent-like object
            const mockEvent = { originalEvent: undefined, item: this.items[0] };
            this.items[0].command(mockEvent);
        }
    }

    // Note: arrow func, otherwise we lose the 'this'
    private goToDesign = (_e: unknown): void => {
        this._router.navigate(['/cell-design', this.design._id]);
    };

    private deleteDesign = (_e: unknown): void => {
        this._confirmationService.confirm({
            message: $localize`:@@common.deleteConfirmationMessage:Are you sure that you want to delete ${this.design.name}:name:?`,
            header: $localize`:@@common.confirm:Confirm`,
            accept: () => {
                this._designService
                    .deleteCellDesign(this.design._id)
                    .pipe(takeUntil(this._ngUnsubscribe))
                    .subscribe((_c) => {
                        this.designChanged();
                        this._messageService.add({
                            key: ToastPositionEnum.topCenter,
                            severity: ToastSeverityEnum.success,
                            summary: $localize`:@@common.success:Success`,
                            detail: $localize`:@@common.itemHasBeenDeleted:${this.design.designId} has been deleted successfully!`,
                        });
                    });
            },
        });
    };

    private openModal = (_e: unknown): void => {
        this.modalOpen = true;
    };
}
