# Further attributes can be found here
# https://bitbucket.org/atlassian/aws-lambda-deploy/src/master/
pipelines:
  # Pipeline for main branch
  branches:
    prod:
      - step:
          name: Construct Version
          artifacts:
            - set_env.sh
          script:
            - export APP_VERSION=$(cat ./VERSION).$(printf '%(%Y%m%d)T\n' -1)-$(git rev-parse HEAD)
            - echo "export APP_VERSION=$APP_VERSION" >> set_env.sh
      - step:
          name: Build & Push image (latest)
          image: node:18-alpine
          oidc: true
          caches:
            - node
          script:
            - source set_env.sh
            # build the image
            - npm ci
            - npm run build
            - DOCKER_BUILDKIT=1 docker build -t cellomat2-global-frontend:$APP_VERSION -t cellomat2-global-frontend:latest -t cellomat2-global-frontend:prod .
            - pipe: atlassian/aws-ecr-push-image:1.6.2
              variables:
                AWS_DEFAULT_REGION: "eu-central-1"
                AWS_OIDC_ROLE_ARN: "arn:aws:iam::014923895365:role/cellomat2-global-frontend-bitbucket-ci-cd-role"
                IMAGE_NAME: "cellomat2-global-frontend"
                TAGS: "$APP_VERSION latest prod"
      - step:
          name: Deploy image to ECS
          deployment: Production
          oidc: true
          image: amazon/aws-cli
          script:
            - yum install -y jq
            - export AWS_REGION=eu-central-1
            - export AWS_ROLE_ARN=arn:aws:iam::014923895365:role/cellomat2-global-frontend-bitbucket-ci-cd-role
            - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
            - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
            - echo "#########################################"
            - echo "# Deploy to $ENV ECS Cluster"
            - echo "#########################################"
            - aws ecs update-service --cluster cellomat2-$ENV-cluster --service cellomat2-$ENV-frontend --force-new-deployment
            - aws ecs wait services-stable --cluster cellomat2-$ENV-cluster --services cellomat2-$ENV-frontend

    main:
      - step:
          name: Construct Version
          artifacts:
            - set_env.sh
          script:
            - export APP_VERSION=$(cat ./VERSION).$(printf '%(%Y%m%d)T\n' -1)-$(git rev-parse HEAD)
            - echo "export APP_VERSION=$APP_VERSION" >> set_env.sh
      - step:
          name: Build & Push image (latest)
          image: node:18-alpine
          oidc: true
          caches:
            - node
          script:
            - source set_env.sh
            # build the image
            - npm ci
            - npm run build
            - DOCKER_BUILDKIT=1 docker build -t cellomat2-global-frontend:$APP_VERSION -t cellomat2-global-frontend:latest -t cellomat2-global-frontend:dev .
            - pipe: atlassian/aws-ecr-push-image:1.6.2
              variables:
                AWS_DEFAULT_REGION: "eu-central-1"
                AWS_OIDC_ROLE_ARN: "arn:aws:iam::014923895365:role/cellomat2-global-frontend-bitbucket-ci-cd-role"
                IMAGE_NAME: "cellomat2-global-frontend"
                TAGS: "$APP_VERSION latest dev"
      - step:
          name: Deploy image to ECS
          deployment: Development
          oidc: true
          image: amazon/aws-cli
          script:
            - yum install -y jq
            - export AWS_REGION=eu-central-1
            - export AWS_ROLE_ARN=arn:aws:iam::014923895365:role/cellomat2-global-frontend-bitbucket-ci-cd-role
            - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
            - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
            - echo "#########################################"
            - echo "# Deploy to $ENV ECS Cluster"
            - echo "#########################################"
            - aws ecs update-service --cluster cellomat2-$ENV-cluster --service cellomat2-$ENV-frontend --force-new-deployment
            - aws ecs wait services-stable --cluster cellomat2-$ENV-cluster --services cellomat2-$ENV-frontend
