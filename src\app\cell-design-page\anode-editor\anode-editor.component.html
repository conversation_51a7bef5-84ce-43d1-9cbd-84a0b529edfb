<div *ngIf="materials && blendableMaterials && design" class="grid align-items-stretch">
    <div class="col-12 lg:col-6">
        <p-card>
            <ng-container
                [ngModelGroup]="formGroupNames.anodeMaterial"
                #anodeMaterialFormGroup="ngModelGroup"
                comValidateGroupSumDirective
                [desiredSum]="desiredSum"
                [includedControlNames]="[controlNames.anodeMaterial1Weight, controlNames.anodeMaterial2Weight]"
                [skipValidation]="true"
            >
                <div class="field grid">
                    <label
                        for="prelithiation_capacity"
                        class="col-12 mb-2 xl:col-3 xl:mb-0"
                        i18n="@@chemicals.anode.prelithiationCapacity.input.label"
                    >
                        Prälithiierung
                    </label>
                    <div class="col-12 xl:col-9 p-fluid">
                        <p-inputNumber
                            comCustomizeInput
                            name="prelithiation_capacity"
                            inputId="prelithiation_capacity"
                            [(ngModel)]="design.prelithiationCapacity"
                            mode="decimal"
                            [minFractionDigits]="2"
                            suffix=" mAh/g"
                            [showClear]="true"
                            [min]="0.001"
                        ></p-inputNumber>
                    </div>
                </div>
                <div class="field grid">
                    <label
                        for="anode_developer_mode"
                        class="col-12 mb-2 xl:col-3 xl:mb-0"
                        i18n="@@chemicals.common.developerMode.checkbox.label"
                    >
                        Entwicklermodus
                    </label>
                    <div class="col-12 xl:col-9 p-fluid">
                        <p-inputSwitch
                            name="anode_developer_mode"
                            inputId="anode_developer_mode"
                            [ngModel]="design.anodeMaterials.length > 1"
                            (ngModelChange)="toggleDeveloperMode($event)"
                        ></p-inputSwitch>
                    </div>
                </div>

                <div class="field grid">
                    <label
                        for="anode_material1_id"
                        class="col-12 mb-2 xl:col-3 xl:mb-0"
                        i18n="@@chemicals.common.material.input.label"
                    >
                        Material {{ 1 }}
                    </label>
                    <div class="col-12 xl:col-9 p-fluid">
                        <p-dropdown
                            name="anode_material1_id"
                            inputId="anode_material1_id"
                            [options]="design.anodeMaterials.length > 1 ? blendableMaterials : materials"
                            [(ngModel)]="design.anodeMaterials[0].materialId"
                            optionLabel="name"
                            optionValue="id"
                        ></p-dropdown>
                    </div>
                </div>

                <div class="field grid" [class.hidden]="!materialMap.get(design.anodeMaterials[0].materialId)?.length">
                    <label
                        for="anode_material1_version_id"
                        class="col-12 mb-2 xl:col-3 xl:mb-0"
                        i18n="@@chemicals.common.material.version.input.label"
                    >
                        Version Material {{ 1 }}
                    </label>
                    <div class="col-12 xl:col-9 p-fluid">
                        <p-dropdown
                            name="anode_material1_version_id"
                            inputId="anode_material1_version_id"
                            [options]="materialMap.get(design.anodeMaterials[0].materialId)!"
                            [(ngModel)]="design.anodeMaterials[0].materialVersionId"
                            optionLabel="description"
                            optionValue="id"
                            [disabled]="materialMap.get(design.anodeMaterials[0].materialId)?.length === 1"
                        >
                            <ng-template let-selectedVersion pTemplate="selectedItem">
                                <div>{{ selectedVersion.description }} - {{ selectedVersion.date | date }}</div>
                            </ng-template>
                            <ng-template let-version pTemplate="item">
                                <div>{{ version.description }} - {{ version.date | date }}</div>
                            </ng-template></p-dropdown
                        >
                    </div>
                </div>

                <ng-container *ngIf="design.anodeMaterials.length > 1">
                    <div class="field grid">
                        <label
                            [for]="controlNames.anodeMaterial1Weight"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@chemicals.common.material.ratio.input.label"
                        >
                            Anteil Material {{ 1 }}
                        </label>
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-inputNumber
                                comCustomizeInput
                                [name]="controlNames.anodeMaterial1Weight"
                                [inputId]="controlNames.anodeMaterial1Weight"
                                [(ngModel)]="design.anodeMaterials[0].weightPercent"
                                #anodeMaterial1WeightField="ngModel"
                                [maxFractionDigits]="2"
                                mode="decimal"
                                suffix=" w%"
                                [min]="0"
                                [max]="100"
                                [required]="true"
                                [ngClass]="{
                                    'ng-invalid ng-dirty': anodeMaterial1WeightField.errors?.[fieldRequiredError]
                                }"
                            ></p-inputNumber>
                        </div>
                        <div *ngIf="anodeMaterialFormGroup.invalid" class="col-12 mb-2 xl:col-3 xl:mb-0"></div>
                        <div
                            *ngIf="anodeMaterialFormGroup.errors?.[groupSumErrorName] || anodeMaterial1WeightField.errors?.[fieldRequiredError]"
                            class="com-text-error col-12 xl:col-9 p-fluid mt-1"
                        >
                            <span class="mr-2"><i class="pi pi-times-circle"></i></span>
                            <span
                                *ngIf="anodeMaterial1WeightField.errors?.[fieldRequiredError] && anodeMaterial1WeightField.touched"
                                i18n="@@swelling.fieldIsRequired"
                            >
                                Feld ist erforderlich.
                            </span>
                            <span
                                *ngIf="!anodeMaterial1WeightField.errors?.[fieldRequiredError] && anodeMaterialFormGroup.errors?.[groupSumErrorName]"
                                i18n="@@materials.percentages.error"
                                >Die Summe der Prozentsätze muss 100 ergeben</span
                            >
                        </div>
                    </div>
                    <div class="field grid">
                        <label
                            for="anode_material2_id"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@chemicals.common.material.input.label"
                        >
                            Material {{ 2 }}
                        </label>
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-dropdown
                                name="anode_material2_id"
                                inputId="anode_material2_id"
                                [options]="remainingBlendableMaterials"
                                [(ngModel)]="design.anodeMaterials[1].materialId"
                                optionLabel="name"
                                optionValue="id"
                            ></p-dropdown>
                        </div>
                    </div>

                    <div
                        class="field grid"
                        [class.hidden]="!materialMap.get(design.anodeMaterials[1].materialId)?.length"
                    >
                        <label
                            for="anode_material2_version_id"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@chemicals.common.material.version.input.label"
                        >
                            Version Material {{ 2 }}
                        </label>
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-dropdown
                                name="anode_material2_version_id"
                                inputId="anode_material2_version_id"
                                [options]="materialMap.get(design.anodeMaterials[1].materialId)!"
                                [(ngModel)]="design.anodeMaterials[1].materialVersionId"
                                optionLabel="description"
                                optionValue="id"
                                [disabled]="materialMap.get(design.anodeMaterials[1].materialId)?.length === 1"
                            >
                                <ng-template let-selectedVersion pTemplate="selectedItem">
                                    <div>{{ selectedVersion.description }} - {{ selectedVersion.date | date }}</div>
                                </ng-template>
                                <ng-template let-version pTemplate="item">
                                    <div>{{ version.description }} - {{ version.date | date }}</div>
                                </ng-template></p-dropdown
                            >
                        </div>
                    </div>

                    <div class="field grid">
                        <label
                            [for]="controlNames.anodeMaterial2Weight"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@chemicals.common.material.ratio.input.label"
                        >
                            Anteil Material {{ 2 }}
                        </label>
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-inputNumber
                                comCustomizeInput
                                [name]="controlNames.anodeMaterial2Weight"
                                [inputId]="controlNames.anodeMaterial2Weight"
                                [(ngModel)]="design.anodeMaterials[1].weightPercent"
                                #anodeMaterial2WeightField="ngModel"
                                [maxFractionDigits]="2"
                                mode="decimal"
                                suffix=" w%"
                                [min]="0"
                                [max]="100"
                                [required]="true"
                                [ngClass]="{
                                    'ng-invalid ng-dirty': anodeMaterial2WeightField.errors?.[fieldRequiredError]
                                }"
                            ></p-inputNumber>
                        </div>
                        <div *ngIf="anodeMaterialFormGroup.invalid" class="col-12 mb-2 xl:col-3 xl:mb-0"></div>
                        <div
                            *ngIf="anodeMaterialFormGroup.invalid && (anodeMaterialFormGroup.errors?.[groupSumErrorName] || anodeMaterial2WeightField.errors?.[fieldRequiredError])"
                            class="com-text-error col-12 xl:col-9 p-fluid mt-1"
                        >
                            <span class="mr-2"><i class="pi pi-times-circle"></i></span>
                            <span
                                *ngIf="anodeMaterial2WeightField.errors?.[fieldRequiredError] && anodeMaterial2WeightField.touched"
                                i18n="@@swelling.fieldIsRequired"
                            >
                                Feld ist erforderlich.
                            </span>
                            <span
                                *ngIf="!anodeMaterial2WeightField.errors?.[fieldRequiredError] && anodeMaterialFormGroup.errors?.[groupSumErrorName]"
                                i18n="@@materials.percentages.error"
                                >Die Summe der Prozentsätze muss 100 ergeben</span
                            >
                        </div>
                    </div>
                </ng-container>
                <ng-container
                    [ngModelGroup]="formGroupNames.anodeQValues"
                    #anodeQValuesFormGroup="ngModelGroup"
                    comValidateQValuesDirective
                    [minimumValue]="300"
                    qRevControlName="anode_q_aim"
                    q1stControlName="anode_q_aim_1"
                >
                    <div class="field grid">
                        <label
                            for="anode_q_aim"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@chemicals.common.qAim.input.label"
                        >
                            Ziel Qrev C/10
                        </label>
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-inputNumber
                                comCustomizeInput
                                name="anode_q_aim"
                                inputId="anode_q_aim"
                                [(ngModel)]="design.anodeQAim"
                                mode="decimal"
                                [minFractionDigits]="2"
                                suffix=" mAh/g"
                                [min]="0.001"
                                [showClear]="true"
                                [ngClass]="{
                                    'ng-invalid ng-dirty': anodeQValuesFormGroup.invalid
                                }"
                            ></p-inputNumber>
                        </div>
                    </div>
                    <div class="field grid">
                        <label
                            for="anode_q_aim_1"
                            class="col-12 mb-2 xl:col-3 xl:mb-0"
                            i18n="@@chemicals.common.qAim1.input.label"
                        >
                            Q 1st Aim
                        </label>
                        <div class="col-12 xl:col-9 p-fluid">
                            <p-inputNumber
                                comCustomizeInput
                                name="anode_q_aim_1"
                                inputId="anode_q_aim_1"
                                [(ngModel)]="design.anodeQAimFirstCharge"
                                mode="decimal"
                                [minFractionDigits]="2"
                                suffix=" mAh/g"
                                [min]="0.001"
                                [showClear]="true"
                                [ngClass]="{
                                    'ng-invalid ng-dirty': anodeQValuesFormGroup.invalid
                                }"
                            ></p-inputNumber>
                        </div>
                        <div *ngIf="anodeQValuesFormGroup.invalid" class="col-12 mb-2 xl:col-3 xl:mb-0"></div>
                        <div *ngIf="anodeQValuesFormGroup.invalid" class="com-text-error col-12 xl:col-9 p-fluid mt-1">
                            <span class="mr-2"><i class="pi pi-times-circle"></i></span>
                            <span
                                *ngIf="anodeQValuesFormGroup.errors?.[qValuesMinimumError]"
                                i18n="@@qValues.minimumValue.error"
                            >
                                Sowohl Ziel Qrev C/10 als auch Ziel Q1st C/10 müssen mindestens {{ 300 }} mAh/g betragen
                            </span>
                            <span
                                *ngIf="!anodeQValuesFormGroup.errors?.[qValuesMinimumError] && anodeQValuesFormGroup.errors?.[qValuesComparisonError]"
                                i18n="@@qValues.comparison.error"
                            >
                                Ziel Q1st C/10 muss größer sein als Ziel Qrev C/10
                            </span>
                        </div>
                    </div>
                </ng-container>
            </ng-container>
        </p-card>

        <p-card>
            <div *ngIf="loading" class="flex justify-content-center">
                <p-progressSpinner></p-progressSpinner>
            </div>

            <com-summary-table
                *ngIf="metrics"
                [headers]="metrics.anode.summary.headers"
                [rows]="metrics.anode.summary.rows"
                [highlightedColumns]="highlightedColumns"
            ></com-summary-table>
        </p-card>
    </div>

    <div class="col-12 lg:col-6">
        <com-material-chart [data]="metrics?.anode?.chart" [options]="options" [loading]="loading" />
    </div>
</div>
