<div class="flex align-items-center" style="width: fit-content">
    <ng-container *ngIf="!isInEdit">
        <span *ngIf="value != null" [ngClass]="{ 'com-text-primary': showUndo }">
            <ng-container *ngTemplateOutlet="displayTemplate; context: { value }" />
        </span>
        <p-button
            *ngIf="!disabled"
            type="button"
            [disabled]="disabled"
            icon="pi pi-pencil"
            styleClass="ml-3 p-button-rounded p-button-text"
            (onClick)="enterEdit()"
        ></p-button>
        <p-button
            *ngIf="!disabled && showUndo"
            [disabled]="disabled"
            type="button"
            icon="pi pi-undo"
            (onClick)="acceptValue(null)"
            styleClass="ml-1 p-button-rounded p-button-text"
        ></p-button>
    </ng-container>

    <ng-container *ngIf="isInEdit && !disabled">
        <ng-container *ngTemplateOutlet="editTemplate; context: { value, setValue }" />
        <p-button
            type="button"
            [disabled]="disabled"
            icon="pi pi-check"
            (onClick)="acceptValue(uncomittedValue)"
            styleClass="p-button-success p-button-rounded p-button-text"
        ></p-button>
        <p-button
            type="button"
            [disabled]="disabled"
            icon="pi pi-times"
            (onClick)="exitEdit()"
            styleClass="p-button-danger p-button-rounded p-button-text"
        ></p-button>
    </ng-container>
</div>
