import { Component, Input, OnC<PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { CellDesignMetrics } from '@com/services/cell-design-metrics.service';
import { CellDesignService, CellDesignWithId } from '@com/services/cell-design.service';
import {
    ToleranceService,
    ToleranceSettings,
    ToleranceVariable,
    ToleranceResult,
    ToleranceCalculationResponse,
    ToleranceValueTypeEnum,
} from '@com/services/tolerance.service';
import { Subject, takeUntil } from 'rxjs';
import { UIChart } from 'primeng/chart';
import { ChartData, ChartOptions } from 'chart.js';
import { calculateUniqueValuesCount, createHistogramData } from './tolerance-utils';

interface ValidationErrors {
    missingMinValue?: boolean;
    missingMaxValue?: boolean;
    missingStandardValue?: boolean;
}

interface ToleranceVariableOption {
    name: string;
    id: string;
    unit: string;
    disabled?: boolean;
}

@Component({
    selector: 'com-tolerance-editor',
    templateUrl: './tolerance-editor.component.html',
    styleUrls: ['./tolerance-editor.component.scss'],
})
export class ToleranceEditorComponent implements OnInit, OnDestroy, OnChanges {
    @ViewChild('histogramChart')
    public histogramChart: UIChart;

    @Input()
    public metrics!: CellDesignMetrics | null;

    @Input()
    public set design(value: CellDesignWithId | undefined) {
        this._design = value;
    }

    public get design(): CellDesignWithId | undefined {
        return this._design;
    }

    public readonly notAvailable = 'N/A';
    public selectedToleranceSettings: ToleranceSettings | null = null;
    public toleranceVariablesOptions: ToleranceVariableOption[] = [];
    public valueTypeOptions = [
        { name: $localize`:@@createTolerance.valueTypeAbsolute:Absolut`, value: ToleranceValueTypeEnum.absolute },
        { name: $localize`:@@createTolerance.valueTypeRelative:Relativ`, value: ToleranceValueTypeEnum.relative },
        {
            name: $localize`:@@createTolerance.valueTypeRelativePercentage:Relativer Prozentsatz`,
            value: ToleranceValueTypeEnum.relativePercentage,
        },
    ];
    public ToleranceValueTypeEnum = ToleranceValueTypeEnum;
    public variableResults: ToleranceResult[] | null = null;
    public chainedResults: ToleranceResult[] | null = null;
    public fullToleranceResults: ToleranceCalculationResponse | null = null;
    public calculatingTolerance = false;
    public toleranceSettingsInvalid = true;
    public selectedVariableCategory: string | null = null;
    public variableCategoryOptions: { name: string; value: string }[] = [];
    public toleranceVariablesValidationErrors: Map<string, ValidationErrors | null> = new Map();
    public standardValuesWithUnit: Map<string, string> = new Map();

    public histogramData: ChartData | null = null;
    public histogramOptions: ChartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false,
            },
            title: {
                display: true,
                text: $localize`:@@createTolerance.chartTitle:Verteilung der Werte`,
            },
        },
        scales: {
            x: {
                title: {
                    display: true,
                    text: $localize`:@@createTolerance.chartXAxis:Wertebereich`,
                },
            },
            y: {
                title: {
                    display: true,
                    text: $localize`:@@createTolerance.chartYAxis:Häufigkeit`,
                },
                beginAtZero: true,
            },
        },
    };

    public selectedHistogramMetric: string | null = null;
    public histogramMetricOptions: { name: string; value: string }[] = [];
    public binCount = 10;
    public readonly minBinCount = 1;
    public maxBinCount = 50;
    private _uniqueValuesCount = 0;

    private _design: CellDesignWithId | undefined;
    private readonly _ngUnsubscribe = new Subject<void>();

    public constructor(private _toleranceService: ToleranceService, private _cellDesignService: CellDesignService) {}

    public ngOnChanges(changes: SimpleChanges): void {
        // Check if design or metrics has changed
        if (
            (changes['design'] && !changes['design'].firstChange) ||
            (changes['metrics'] && !changes['metrics'].firstChange)
        ) {
            // Clear tolerance results when design or metrics change
            this.variableResults = null;
            this.chainedResults = null;
            this.fullToleranceResults = null;
            this.updateAllVariablesValidation();
        }
    }

    public ngOnInit(): void {
        this._cellDesignService
            .getCurrentDesign()
            .pipe(takeUntil(this._ngUnsubscribe))
            .subscribe(() => {
                if (this.selectedToleranceSettings && this.selectedToleranceSettings.variables.length > 0) {
                    this.updateAllVariablesValidation();
                    this.toleranceSettingsInvalid = this.areToleranceVariablesInvalid();
                }
            });

        this._toleranceService
            .getToleranceVariables()
            .pipe(takeUntil(this._ngUnsubscribe))
            .subscribe((response) => {
                this.toleranceVariablesOptions = response;

                if (this.design?.toleranceSettings) {
                    this.selectedToleranceSettings = this.design.toleranceSettings;
                } else {
                    const initialToleranceSettings: ToleranceSettings = {
                        variables: [],
                        valueType: ToleranceValueTypeEnum.absolute,
                    };

                    this.selectedToleranceSettings = initialToleranceSettings;

                    // Save the initial settings to the design
                    if (this.design) {
                        this.design.toleranceSettings = this.selectedToleranceSettings;
                    }
                }

                this.updateToleranceVariablesOptions();
                this.updateAllVariablesValidation();
                this.updateToleranceSettingsValidity();
            });
    }

    public ngOnDestroy(): void {
        this._ngUnsubscribe.next();
        this._ngUnsubscribe.complete();
    }

    public onVariableChange(variableId: string, index: number): void {
        if (!this.selectedToleranceSettings) {
            return;
        }

        const selectedVariable = this.selectedToleranceSettings.variables[index];
        if (!selectedVariable) {
            return;
        }

        const variableOption = this.toleranceVariablesOptions.find((v) => v.id === variableId);
        if (variableOption) {
            selectedVariable.name = variableOption.name;
            selectedVariable.unit = variableOption.unit;
        }

        this.handleToleranceVariableChange(selectedVariable);
    }

    public removeToleranceVariable(index: number): void {
        if (!this.selectedToleranceSettings) {
            return;
        }

        const removedVariable = this.selectedToleranceSettings.variables[index];
        if (removedVariable) {
            this.toleranceVariablesValidationErrors.delete(removedVariable.id);
        }

        this.selectedToleranceSettings.variables.splice(index, 1);
        this.handleToleranceVariableChange();
    }

    public addToleranceVariable(): void {
        if (!this.selectedToleranceSettings) {
            return;
        }

        const availableOption = this.toleranceVariablesOptions.find(
            (option) => !this.selectedToleranceSettings?.variables.some((variable) => variable.id === option.id)
        );

        if (availableOption) {
            const newVariable = {
                id: availableOption.id,
                name: availableOption.name,
                unit: availableOption.unit,
                lowerLimit: 5,
                upperLimit: 10,
            };

            this.selectedToleranceSettings.variables.push(newVariable);
            this.handleToleranceVariableChange(newVariable);
        }
    }

    public handleToleranceVariableChange(variable?: ToleranceVariable): void {
        this.updateToleranceVariablesOptions();
        this.saveToleranceSettingsToDesign();
        if (variable) {
            this.updateVariableValidation(variable);
        }

        this.updateToleranceSettingsValidity();
        this.variableResults = null;
        this.chainedResults = null;
        this.fullToleranceResults = null;
    }

    public updateVariableValidation(variable: ToleranceVariable): void {
        const errors = this.validateToleranceVariable(variable);
        this.toleranceVariablesValidationErrors.set(variable.id, errors);
        this.updateStandardValueWithUnit(variable);
    }

    public saveToleranceSettingsToDesign(): void {
        if (!this.design || !this.selectedToleranceSettings) {
            return;
        }

        // Save the tolerance settings to the design object
        this.design.toleranceSettings = this.selectedToleranceSettings;
    }

    public onValueTypeChange(): void {
        if (!this.selectedToleranceSettings) {
            return;
        }

        this.saveToleranceSettingsToDesign();
        // Clear tolerance results as they are no longer valid with the new value type
        this.variableResults = null;
        this.chainedResults = null;
        this.fullToleranceResults = null;
    }

    public updateToleranceSettingsValidity(): void {
        this.toleranceSettingsInvalid = this.areToleranceVariablesInvalid();
    }

    public calculateTolerance(): void {
        if (!this.design || this.toleranceSettingsInvalid) {
            return;
        }

        this.calculatingTolerance = true;
        this.variableResults = null;
        this.chainedResults = null;
        this.fullToleranceResults = null;
        this.selectedVariableCategory = null;
        this.variableCategoryOptions = [];
        this.selectedHistogramMetric = null;
        this.histogramMetricOptions = [];
        this.histogramData = null;

        this._toleranceService
            .calculateTolerance(this.design)
            .pipe(takeUntil(this._ngUnsubscribe))
            .subscribe((results) => {
                this.fullToleranceResults = results;
                this.chainedResults = results.chainedResult;

                // Create options for variable category dropdown with friendly names
                this.variableCategoryOptions = Object.keys(results.variableResults).map((key) => {
                    const variableOption = this.toleranceVariablesOptions.find((v) => v.id === key);

                    return {
                        name: variableOption ? variableOption.name : key,
                        value: key,
                    };
                });

                // Create histogram metric options from chained results
                if (this.chainedResults) {
                    this.histogramMetricOptions = this.chainedResults
                        .filter((result) => result.allValues && result.allValues.length > 0)
                        .map((result) => ({
                            name: result.name,
                            value: result.id,
                        }));

                    if (this.histogramMetricOptions.length > 0) {
                        this.selectedHistogramMetric = this.histogramMetricOptions[0].value;
                        this.updateHistogramChart();
                    }
                }

                if (this.variableCategoryOptions.length > 0) {
                    this.selectedVariableCategory = this.variableCategoryOptions[0].value;
                    this.onVariableCategoryChange();
                }

                this.calculatingTolerance = false;
            });
    }

    public onVariableCategoryChange(): void {
        if (!this.fullToleranceResults || !this.selectedVariableCategory) {
            this.variableResults = null;

            return;
        }

        this.variableResults = this.fullToleranceResults.variableResults[this.selectedVariableCategory];
    }

    public onHistogramMetricChange(): void {
        this.binCount = 10;
        this.updateHistogramChart();
    }

    public getSelectedVariableName(): string {
        if (!this.selectedVariableCategory) {
            return '';
        }

        const selectedOption = this.variableCategoryOptions.find(
            (option) => option.value === this.selectedVariableCategory
        );

        return selectedOption ? selectedOption.name : this.selectedVariableCategory;
    }

    public hasMultipleVariables(results: ToleranceCalculationResponse | null): boolean {
        if (!results || !results.variableResults) {
            return false;
        }
        return Object.keys(results.variableResults).length > 1;
    }

    public updateHistogramChart(): void {
        const selectedResult = this.chainedResults?.find((r) => r.id === this.selectedHistogramMetric);
        const validValues = selectedResult?.allValues?.filter((value): value is number => value !== null) ?? [];
        if (!selectedResult || validValues.length === 0) {
            this.histogramData = null;

            return;
        }

        this._uniqueValuesCount = calculateUniqueValuesCount(validValues);
        this.maxBinCount = Math.max(this.minBinCount, this._uniqueValuesCount);

        if (this.binCount > this.maxBinCount) {
            this.binCount = this.maxBinCount;
        }

        this.histogramData = createHistogramData(selectedResult, validValues, this.binCount, this._uniqueValuesCount);

        this.histogramOptions = {
            ...this.histogramOptions,
            plugins: {
                ...this.histogramOptions.plugins,
                title: {
                    display: true,
                    text: `${selectedResult.name}${selectedResult.unit ? ` (${selectedResult.unit})` : ''}`,
                },
            },
        };
    }

    private getStandardValue(variableId: string): number | string {
        if (!this.metrics || !this.metrics.electrodePair) {
            if (!this.design) {
                return this.notAvailable;
            }

            const value = this.design[variableId as keyof CellDesignWithId] as number;

            return value !== undefined && value !== null ? value : this.notAvailable;
        }

        // Map variableId to the correct metrics property if available
        const metricMap: Record<string, number | undefined> = {
            anodeLoading: this.metrics.electrodePair.anodeLoading,
            cathodeLoading: this.metrics.electrodePair.cathodeLoading,
            npRatioRev: this.metrics.electrodePair.balancing,
            anodeCoating: this.metrics.electrodePair.anodeCoatingThickness,
            cathodeCoating: this.metrics.electrodePair.cathodeCoatingThickness,
        };

        if (Object.prototype.hasOwnProperty.call(metricMap, variableId)) {
            const metricValue = metricMap[variableId];
            if (metricValue === undefined || metricValue === null) {
                return this.notAvailable;
            }

            return metricValue.toFixed(2);
        }

        if (!this.design) {
            return this.notAvailable;
        }

        const value = this.design[variableId as keyof CellDesignWithId] as number;

        return value !== undefined && value !== null ? value : this.notAvailable;
    }

    private getStandardValueWithUnit(variableId: string, unit: string): string {
        const value = this.getStandardValue(variableId);
        if (value === this.notAvailable) return this.notAvailable;

        return unit ? `${value} ${unit}` : value.toString();
    }

    private updateStandardValueWithUnit(variable: ToleranceVariable): void {
        const formattedValue = this.getStandardValueWithUnit(variable.id, variable.unit);
        this.standardValuesWithUnit.set(variable.id, formattedValue);
    }

    private updateAllVariablesValidation(): void {
        if (!this.selectedToleranceSettings) {
            return;
        }

        this.toleranceVariablesValidationErrors.clear();
        this.standardValuesWithUnit.clear();

        for (const variable of this.selectedToleranceSettings.variables) {
            this.updateVariableValidation(variable);
        }
    }

    private validateToleranceVariable(variable: ToleranceVariable): ValidationErrors | null {
        if (!this.design) return null;

        const errors: ValidationErrors = {};

        if (variable.lowerLimit === undefined || variable.lowerLimit === null) {
            errors.missingMinValue = true;
        }

        if (variable.upperLimit === undefined || variable.upperLimit === null) {
            errors.missingMaxValue = true;
        }

        // Check if the standard value is missing in the cell design
        const standardValue = this.getStandardValue(variable.id);
        if (standardValue === this.notAvailable) {
            errors.missingStandardValue = true;
        }

        return Object.keys(errors).length ? errors : null;
    }

    private areToleranceVariablesInvalid(): boolean {
        if (!this.design || !this.selectedToleranceSettings || this.selectedToleranceSettings.variables.length === 0) {
            return true;
        }
        return this.selectedToleranceSettings.variables.some(
            (v) => this.toleranceVariablesValidationErrors.get(v.id) !== null
        );
    }

    private updateToleranceVariablesOptions(): void {
        if (!this.selectedToleranceSettings) {
            return;
        }

        const selectedIds = this.selectedToleranceSettings.variables.map((variable) => variable.id);
        this.toleranceVariablesOptions = this.toleranceVariablesOptions.map((option) => ({
            ...option,
            disabled: selectedIds.includes(option.id),
        }));
    }
}
