@use "./variables.scss" as variables;

.width-50 {
    width: 50%;
}

.break-word {
    word-break: break-word;
}

.mobile-only {
    display: none;
}

@media (max-width: 991px) {
    .mobile-only {
        display: initial;
    }
}

.com-text-primary {
    color: variables.$logoPrimeColor;
}

.com-text-warning {
    color: variables.$warningColor;
}

.com-text-error {
    color: variables.$errorColor;
}
