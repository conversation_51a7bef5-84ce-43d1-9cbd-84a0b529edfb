<p-confirmDialog></p-confirmDialog>
<form #generalForm="ngForm" name="general">
    <p-dialog
        *ngIf="editableDesign"
        [(visible)]="dialogVisible"
        [style]="{ minWidth: '50vw' }"
        [modal]="true"
        [draggable]="false"
        (onHide)="hideDialog()"
    >
        <ng-template pTemplate="header">
            <h3 class="m-0">{{ dialogConfig.get(dialogType)?.header }}</h3>
        </ng-template>

        <div *ngIf="loading" class="flex justify-content-center align-items-center progress-spinner">
            <p-progressSpinner></p-progressSpinner>
        </div>

        <div class="field grid">
            <label [for]="controlNames.name" class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@common.name">Name</label>
            <div class="col-12 xl:col-9 p-fluid">
                <input
                    pInputText
                    [name]="controlNames.name"
                    [id]="controlNames.name"
                    [maxlength]="1000"
                    [(ngModel)]="editableDesign.name"
                    [required]="true"
                    autocomplete="off"
                />
            </div>
        </div>

        <div class="field grid">
            <label [for]="controlNames.projectName" class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@common.projectName"
                >Projektname</label
            >
            <div class="col-12 xl:col-9 p-fluid">
                <p-autoComplete
                    [(ngModel)]="editableDesign.projectName"
                    [suggestions]="projectSuggestions"
                    [name]="controlNames.projectName"
                    [inputId]="controlNames.projectName"
                    [maxlength]="50"
                    [dropdown]="true"
                    (completeMethod)="searchProjects($event)"
                    [required]="true"
                ></p-autoComplete>
            </div>
        </div>

        <div class="field grid">
            <label [for]="controlNames.projectState" class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@common.projectState"
                >Musterstand</label
            >
            <div class="col-12 xl:col-9 p-fluid">
                <p-autoComplete
                    [(ngModel)]="editableDesign.projectState"
                    [suggestions]="projectStateSuggestions"
                    [name]="controlNames.projectState"
                    [inputId]="controlNames.projectState"
                    [maxlength]="50"
                    [dropdown]="true"
                    (completeMethod)="searchProjectStates($event)"
                ></p-autoComplete>
            </div>
        </div>

        <div class="field grid">
            <label [for]="controlNames.partNumber" class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@common.partNumber"
                >Part Nummer</label
            >
            <div class="col-12 xl:col-9 p-fluid">
                <input
                    pInputText
                    [name]="controlNames.partNumber"
                    [id]="controlNames.partNumber"
                    [maxlength]="50"
                    [(ngModel)]="editableDesign.partNumber"
                    autocomplete="off"
                />
            </div>
        </div>

        <div class="field grid">
            <label [for]="controlNames.description" class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@common.description"
                >Beschreibung</label
            >
            <div class="col-12 xl:col-9 p-fluid">
                <textarea
                    pInputTextarea
                    [(ngModel)]="editableDesign.description"
                    [name]="controlNames.description"
                    [maxlength]="120"
                    [required]="true"
                    autocomplete="off"
                ></textarea>
            </div>
        </div>

        <div class="field grid">
            <label [for]="controlNames.releaseStatus" class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@common.releaseStatus"
                >Freigabenstatus</label
            >
            <div class="col-12 xl:col-9 p-fluid">
                <p-dropdown
                    [disabled]="!canRelease || (design?.released ?? false)"
                    [options]="releaseOptions"
                    optionLabel="name"
                    optionValue="value"
                    [name]="controlNames.releaseStatus"
                    [inputId]="controlNames.releaseStatus"
                    [(ngModel)]="editableDesign.released"
                ></p-dropdown>
            </div>
        </div>

        <div class="field grid">
            <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@common.releasedDate">Freigabendatum</label>
            <div class="col-12 xl:col-9 p-fluid">
                {{ editableDesign.releasedAt | date }}
            </div>
        </div>

        <div class="field grid">
            <label class="col-12 mb-2 xl:col-3 xl:mb-0" i18n="@@common.releasedBy">Freigeben von</label>
            <div class="col-12 xl:col-9 p-fluid">
                {{ editableDesign.releasedBy?.name }}
            </div>
        </div>

        <ng-template pTemplate="footer">
            <p-button
                icon="pi pi-times"
                (click)="hideDialog()"
                i18n-label="@@common.cancel"
                label="Cancel"
                styleClass="p-button-secondary"
            ></p-button>
            <p-button
                icon="pi pi-check"
                (click)="onConfirm()"
                type="submit"
                [disabled]="loading"
                styleClass="p-button-primary"
                [label]="dialogConfig.get(dialogType)?.buttonLabel ?? 'Save'"
            ></p-button>
        </ng-template>
    </p-dialog>
</form>
