import { Observable, OperatorFunction, Subscriber } from 'rxjs';
import { deepCloneDto } from './object';

export class RxHistory<T> {
    private _capacity: number | null;

    private _history: T[] = [];
    private _head: number = 0;

    private _subscribers: Subscriber<{ value: T; history: boolean }>[] = [];

    public constructor(capacity: number | null = null) {
        this._capacity = capacity;
    }

    public clear(): void {
        this._history.length = 0;
        this._head = 0;
    }

    public canUndo(): boolean {
        return this._head > 1;
    }

    public canRedo(): boolean {
        return this._head < this._history.length;
    }

    public undo(): void {
        if (this.canUndo()) {
            const value = this._history[--this._head - 1];
            this._subscribers.forEach((s) => s.next({ value: value, history: true }));
        }
    }

    public redo(): void {
        if (this.canRedo()) {
            const value = this._history[++this._head - 1];
            this._subscribers.forEach((s) => s.next({ value: value, history: true }));
        }
    }

    public inject(): OperatorFunction<T, { value: T; history: boolean }> {
        return (source) => {
            return new Observable<{ value: T; history: boolean }>((subscriber) => {
                const subscription = source.subscribe({
                    next: (value) => {
                        // trim history to head
                        this._history.length = this._head;

                        if (this._capacity == null || this._capacity >= 0) {
                            if (this._capacity != null && this._capacity === this._history.length) {
                                // capacity is reached
                                // remove first value from the queue to make space for the new value
                                this._history.shift();
                            }

                            // save deep copy of history value
                            this._history.push(deepCloneDto(value));
                            ++this._head;
                        }

                        // emit value directly
                        subscriber.next({ value: value, history: false });
                    },
                    complete: () => {
                        subscriber.complete();
                    },
                    error: (err) => {
                        subscriber.error(err);
                    },
                });

                this._subscribers.push(subscriber);

                return () => {
                    // teardown

                    // remove subscriber from the list
                    this._subscribers.splice(this._subscribers.indexOf(subscriber), 1);

                    // unsubscribe from the source
                    subscription.unsubscribe();
                };
            });
        };
    }
}
