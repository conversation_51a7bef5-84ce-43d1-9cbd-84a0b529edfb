<div *ngIf="!design?.optimizationId" class="p-4">
    <span i18n="@@parameterOptimization.noOptimizationMessage"
        ><PERSON><PERSON><PERSON> dies<PERSON> Design steht aktuell keine Optimierung zur Verfügung.</span
    >
</div>

<div
    *ngIf="cellOptimization && cellOptimization.status === OptimizationStatusEnum.pending"
    class="flex flex-column align-items-center p-4"
>
    <span i18n="@@parameterOptimization.optimizationRunning" class="text-center mb-4"
        >Optimierung im Gange. Sobald dieser abgeschlossen ist, werden die Ergebnisse hier angezeigt. Bitte warte.</span
    >
    <div class="w-6">
        <div class="text-center mt-2 mb-2 flex align-items-center justify-content-center gap-1">
            <p-progressSpinner styleClass="w-2rem h-2rem"></p-progressSpinner>
            <span style="display: inline-block; width: 6ch; text-align: left">
                <span i18n="@@parameterOptimization.loading">Laden</span>{{ loadingDots }}
            </span>
        </div>
        <p-progressBar [value]="cellOptimization.progress" [showValue]="true" class="w-full"></p-progressBar>

        <div *ngIf="timeRemaining" class="text-center mt-2" i18n="@@parameterOptimization.timeRemaining">
            <span>Geschätzte verbleibende Zeit: {{ timeRemaining || "N/A" }}</span>
        </div>
    </div>
</div>

<div
    i18n="@@parameterOptimization.optimizationFailed"
    *ngIf="cellOptimization && cellOptimization.status === OptimizationStatusEnum.error"
    class="p-4"
>
    <span>Optimierung fehlgeschlagen. Bitte erstelle eine neue.</span>
</div>
<div
    *ngIf="cellOptimization && cellOptimization.status === OptimizationStatusEnum.complete"
    class="grid align-items-stretch"
>
    <div *ngIf="design && cellOptimization.variables" class="col-12 xl:col-6 pb-5 flex flex-column gap-5">
        <p-panel>
            <ng-template pTemplate="header">
                <strong i18n="@@parameterOptimization.optimizationSettings" class="text-xl"
                    >Optimierungseinstellungen</strong
                >
            </ng-template>
            <div class="grid align-items-center">
                <label
                    i18n="@@createOptimization.optimizationObjective"
                    for="upperLimit"
                    class="col-12 sm:col-4 lg:col-4"
                    >Optimierungsziel</label
                >
                <span class="col-12 lg:col-5 p-fluid">{{ cellOptimization.objective.name }}</span>
            </div>

            <div class="grid align-items-center mb-4">
                <label
                    i18n="@@createOptimization.optimizationAlgorithm"
                    for="upperLimit"
                    class="col-12 sm:col-4 lg:col-4"
                    >Optimierungsalgorithmus</label
                >
                <span class="col-12 lg:col-5 p-fluid">{{ cellOptimization.algorithm.name }}</span>
            </div>

            <hr class="col-span-12" />

            <div class="grid mb-4">
                <span i18n="@@createOptimization.optimizationVariables" class="col-6">OptimierungSvariablen</span>
                <label i18n="@@createOptimization.lowerLimit" for="lowerLimit" class="col-2">Unteres Limit</label>
                <label i18n="@@createOptimization.upperLimit" for="upperLimit" class="col-2">Oberes Limit</label>
                <label
                    *ngIf="cellOptimization.algorithm.id !== OptimizationAlgorithmEnum.bayesian"
                    i18n="@@createOptimization.stepSize"
                    for="stepSize"
                    class="col-2"
                    >Limit Schrittgröße</label
                >

                <ng-container *ngFor="let variable of cellOptimization.variables; let i = index">
                    <div class="col-6 flex">
                        <span>{{ variable.name }}</span>
                    </div>

                    <div class="col-2 p-fluid">
                        <span>{{ variable.minValue }}{{ variable.unit ? " " + variable.unit : "" }}</span>
                    </div>

                    <div class="col-2 p-fluid">
                        <span>{{ variable.maxValue }}{{ variable.unit ? " " + variable.unit : "" }}</span>
                    </div>

                    <div
                        *ngIf="cellOptimization.algorithm.id !== OptimizationAlgorithmEnum.bayesian"
                        class="col-2 p-fluid"
                    >
                        <span>{{ variable.stepSize }}{{ variable.unit ? " " + variable.unit : "" }}</span>
                    </div>
                </ng-container>
            </div>

            <hr *ngIf="cellOptimization.algorithm.id === OptimizationAlgorithmEnum.bayesian" class="col-span-12" />

            <div *ngIf="cellOptimization.algorithm.id === OptimizationAlgorithmEnum.bayesian" class="grid">
                <span class="col-12" i18n="@@createOptimization.advancedSettings">Erweiterte Einstellungen</span>

                <label class="col-12 sm:col-6 lg:col-4" i18n="@@createOptimization.numInitialPoints"
                    >Anzahl der Anfangspunkte</label
                >
                <span class="col-12 sm:col-6 lg:col-6">{{ cellOptimization.numInitialPoints }}</span>

                <label i18n="@@createOptimization.numIterations" class="col-12 sm:col-6 lg:col-4"
                    >Anzahl der Iterationen</label
                >
                <span class="col-12 sm:col-6 lg:col-6 p-fluid">{{ cellOptimization.numIterations }} </span>
            </div>
        </p-panel>

        <p-panel *ngIf="cellOptimization.optimizationOutcome">
            <ng-template pTemplate="header">
                <strong i18n="@@parameterOptimization.optimizationOutcome" class="text-xl">Optimierungsergebnis</strong>
            </ng-template>
            <div class="grid mb-4">
                <span i18n="@@createOptimization.optimizationVariables" class="col-6">OptimierungSvariablen</span>
                <label i18n="@@parameterOptimization.beforeOptimization" for="lowerLimit" class="col-2 text-center"
                    >Vor der Optimierung</label
                >
                <label i18n="@@parameterOptimization.afterOptimization" for="upperLimit" class="col-2 text-center"
                    >Nach der Optimierung</label
                >

                <ng-container *ngFor="let variable of cellOptimization.variables; let i = index">
                    <div class="col-6 flex text-center">
                        <span>{{ variable.name }}</span>
                    </div>

                    <div class="col-2 p-fluid text-center">
                        <span>{{ variable.original }}{{ variable.unit ? " " + variable.unit : "" }}</span>
                    </div>

                    <div class="col-2 p-fluid text-center">
                        <span
                            >{{ variable.optimal | numberTwoFractionDigits
                            }}{{ variable.unit ? " " + variable.unit : "" }}</span
                        >
                    </div>
                </ng-container>
            </div>

            <hr class="col-span-12" />
            <div class="grid">
                <span i18n="@@parameterOptimization.cellKPI" class="col-6">Zell-KPI</span>
                <label i18n="@@parameterOptimization.beforeOptimization" for="lowerLimit" class="col-2 text-center"
                    >Vor der Optimierung</label
                >
                <label i18n="@@parameterOptimization.afterOptimization" for="upperLimit" class="col-2 text-center"
                    >Nach der Optimierung</label
                >
                <label i18n="@@parameterOptimization.efficiencyGain" for="stepSize" class="col-2 text-center"
                    >Effizienzsteigerung</label
                >
                <ng-container *ngFor="let objective of cellOptimization.optimizationOutcome; let i = index">
                    <div class="col-6 flex">
                        <span class="">{{ objective.name }}</span>
                    </div>

                    <div class="col-2 p-fluid text-center">
                        <span>
                            {{
                                objective.originalValue !== null && objective.originalValue !== undefined
                                    ? (objective.originalValue | numberTwoFractionDigits) +
                                      (objective.unit ? " " + objective.unit : "")
                                    : " - "
                            }}
                        </span>
                    </div>

                    <div class="col-2 p-fluid text-center">
                        <span>
                            {{
                                objective.optimizedValue !== null && objective.optimizedValue !== undefined
                                    ? (objective.optimizedValue | numberTwoFractionDigits) +
                                      (objective.unit ? " " + objective.unit : "")
                                    : " - "
                            }}
                        </span>
                    </div>

                    <div class="col-2 p-fluid text-center">
                        <strong
                            *ngIf="objective.percentageOptimization"
                            [ngClass]="{
                                'text-green-500': objective.percentageOptimization > 0,
                                'text-red-500': objective.percentageOptimization < 0
                            }"
                        >
                            {{ objective.percentageOptimization | numberTwoFractionDigits }} %
                        </strong>
                        <span *ngIf="!objective.percentageOptimization"> - </span>
                    </div>
                </ng-container>
            </div>
        </p-panel>
    </div>
    <div class="col-12 xl:col-6">
        <div class="grid align-items-center">
            <label i18n="@@parameterOptimization.chartType" class="ml-6 mr-4">Diagrammtyp</label>
            <p-dropdown
                name="optimizationVariable"
                [inputId]="'optimizationVariable'"
                [options]="chartTypeOptions"
                [(ngModel)]="chartType"
                (ngModelChange)="onChartTypeChange($event)"
                optionLabel="name"
                optionValue="id"
                class="lg:col-5 p-fluid"
            ></p-dropdown>
        </div>
        <p-chart
            *ngIf="cellOptimization"
            height="40rem"
            [type]="chartType"
            [data]="cellOptimization.chart"
            [options]="options"
        />
        <div class="grid align-items-center">
            <label i18n="@@parameterOptimization.optimizationVariable" for="upperLimit" class="ml-6 m-4"
                >OptimierungSvariable</label
            >
            <p-dropdown
                *ngIf="uiChart"
                name="tset"
                [inputId]="'test'"
                [options]="chartDatasetOptions"
                [(ngModel)]="selectedChartDataset"
                (ngModelChange)="onChartDatasetChange($event)"
                optionLabel="name"
                optionValue="id"
                class="lg:col-6 p-fluid"
            ></p-dropdown>
        </div>
    </div>
</div>
