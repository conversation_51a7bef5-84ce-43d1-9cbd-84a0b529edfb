import { MonoTypeOperatorFunction, pipe } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { deepEquals } from './deep-equals';
import { deepCloneDto } from './object';

export function distinctUntilChangedDeepCopy<T>(): MonoTypeOperatorFunction<T> {
    let isFirst = true;
    let last: T | null = null;

    return pipe(
        filter((value) => {
            // check if value changed
            if (isFirst || !deepEquals(last, value)) {
                isFirst = false;

                return true;
            } else {
                return false;
            }
        }),
        map((value) => {
            // store deep copy and emit
            const copy = deepCloneDto(value);
            last = copy;

            return value;
        })
    );
}
