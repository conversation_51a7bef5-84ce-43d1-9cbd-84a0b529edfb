<div *ngIf="design" class="p-3">
    <div class="flex justify-content-around flex-wrap">
        <div *ngIf="businessCaseOptions" class="field grid">
            <label [for]="controlNames.bomDataSetType" i18n="@@bom.input.dataSet.label" class="xl:mb-0">Data Set</label>
            <div class="ml-2">
                <p-dropdown
                    [name]="controlNames.bomDataSetType"
                    [inputId]="controlNames.bomDataSetType"
                    [options]="businessCaseOptions"
                    [(ngModel)]="design.businessCaseId"
                    optionLabel="label"
                    optionValue="_id"
                    [required]="true"
                ></p-dropdown>
            </div>
        </div>
        <div class="field grid">
            <label [for]="controlNames.scrapMaterial" i18n="@@bom.input.scrapMaterial.label" class="xl:mb-0">
                Ausschuss Material
            </label>
            <div class="ml-2">
                <p-inputNumber
                    comCustomizeInput
                    [name]="controlNames.scrapMaterial"
                    [inputId]="controlNames.scrapMaterial"
                    [(ngModel)]="design.scrap"
                    [maxFractionDigits]="2"
                    mode="decimal"
                    suffix=" %"
                    [min]="0.0001"
                    [max]="1000"
                    [required]="true"
                ></p-inputNumber>
            </div>
        </div>
    </div>

    <div *ngIf="loading" class="flex justify-content-center">
        <p-progressSpinner></p-progressSpinner>
    </div>

    <com-summary-table
        *ngIf="metrics"
        [headers]="metrics.headers"
        [hasCellBeenEdited]="hasCellBeenEdited"
        [rows]="metrics.rows"
        (cellValueChange)="onCellValueChange($event)"
        (cellValueReset)="onCellValueReset($event)"
    ></com-summary-table>
</div>
