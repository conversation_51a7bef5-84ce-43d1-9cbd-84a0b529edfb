import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { AnnotationTypeEnum } from '@com/const';
import { CellDesign } from './cell-design.service';
import { ConfigService } from './config.service';

export interface CellDesignMetrics {
    cathode: {
        summary: MaterialSummaryTable;
        chart: MaterialLineChartData;
    };
    anode: {
        summary: MaterialSummaryTable;
        chart: MaterialLineChartData;
    };
    balancing: BalancingMetrics;
    bom: BomMetrics;
    materials: MaterialsMetrics;
    electrodePair: ElectrodePairMetrics;
    summary: CellSummaryMetrics;
    electrolyte: ElectrolyteMetrics;
    cell: CellEditorMetrics;
    cellSwelling: CellSwellingMetrics;
}

export interface CellSummaryMetrics {
    cellWeightOverall: number;
    cellVolumeOverall: number;
    priceCell?: number;
    safeNominalVoltageC10?: number;
    safeCellCapacityC10?: number;
    safeCellEnergyC10?: number;
    safeCellEnergyDensityVolumetricC10?: number;
    safeCellEnergyDensityGravimetricC10?: number;
    safeCellPriceKwhC10?: number;
    safeNominalVoltageC3?: number;
    safeCellCapacityC3?: number;
    safeCellEnergyC3?: number;
    safeCellEnergyDensityVolumetricC3?: number;
    safeCellEnergyDensityGravimetricC3?: number;
    safeCellPriceKwhC3?: number;
    cellCount: number;
    packNominalVoltage?: number;
    safePackCapacity?: number;
    safePackEnergy?: number;
    packWeight?: number;
    packPrice?: number;
}

export interface BomMetrics {
    table: MaterialSummaryTable;
}

export interface BalancingMetrics {
    chart: MaterialLineChartData;
    chartAnnotations: BoxAnnotation[];
    npRatioRev: number;
    hysteresis: number;
    uMin: number;
    uMax: number;
    summary: MaterialSummaryTable;
    warning: string;
}

export interface ElectrolyteMetrics {
    poreVolumeAh: number;
    poreVolume: number;
    electrolyteAmount: number;
    electrolyteAmountSuggestionAh: number;
    electrolyteAmountSuggestion: number;
    electrolyteAmountSei: number;
    firstCycleEfficiency: number;
    seiGrowthMlAh: number;
    seiGrowthNmAh: number;
    anodeActiveSurface: number;

    agingTable: MaterialSummaryTable;
}

export interface ElectrodePairMetrics {
    cathodePorosity: number;
    anodePorosity: number;
    balancing: number;
    anodeAreaCapacity: number;
    cathodeLoading: number;
    anodeLoading: number;
    cathodeCoatingThickness: number;
    anodeCoatingThickness: number;
    cellLayerThickness: number;
    cathodeThickness: number;
    anodeThickness: number;
    designDatasets: ElectrodePairDesignSection[];
}

interface ElectrodePairDesignSection {
    label: string;
    backgroundColor: string;
    data: number[];
}

export interface BoxAnnotation {
    label: { display: boolean; content: string; color?: string; rotation?: number };
    id: string;
    type: AnnotationTypeEnum.box;
    xMin?: number;
    xMax?: number;
    drawTime: 'beforeDatasetsDraw';
    backgroundColor: string;
}

export interface MaterialsMetrics {
    anode: ElectrodeMaterialsMetrics;
    cathode: ElectrodeMaterialsMetrics;

    prelithiation: {
        lithiumWeightPercent: number;
        lithiumDensity: number;

        activeMaterialWeightPercent: number;
        material1WeightPercent: number;
        material2WeightPercent?: number;

        binderMaterials: { materialId: string; weightPercent: number; density: number }[];
        conductiveAdditiveMaterials: { materialId: string; weightPercent: number; density: number }[];
    };

    electrolyteDensity: number;
    separatorDensity: number;
    separatorPorousness: number;
    aluminiumDensity: number;
    copperDensity: number;
}

export interface ElectrodeMaterialsMetrics {
    activeMaterialDensity: number;

    material1WeightPercent: number;
    material1Density: number;

    material2WeightPercent?: number;
    material2Density?: number;

    binderMaterials: { materialId: string; weightPercent: number; density: number }[];
    conductiveAdditiveMaterials: { materialId: string; weightPercent: number; density: number }[];

    totalDensity: number;
    fullCellQrev: number;
    halfCellQrev: number;
}

export interface MaterialSummaryTable {
    headers: string[];
    rows: MaterialSummaryTableRow[];
}

export interface MaterialSummaryTableRow {
    cells: MaterialSummaryTableRowCell[];
    isPrimary?: boolean;
}

export interface MaterialSummaryTableRowCell {
    unit?: string | null;
    value: SummaryCellAcceptedTypes;
    editable?: TableRowCellEditableApiData;
}

export interface TableRowCellEditableApiData {
    id: string;
    min?: number;
    max?: number;
}

export type SummaryCellAcceptedTypes = number | string;

export interface MaterialLineChartData {
    headers: string[];
    datasets: MaterialLineChartDataset[];
}

export interface MaterialLineChartDataset {
    id: string | null;
    label: string;
    description: string;
    hidden: boolean;
    data: { x: number; y: number }[];
    borderColor: string;
    borderDash: number[];
    borderWidth: number;
}

export interface CellEditorMetrics {
    cellVolume: number; // Zellvolumen [ml]
    housingWeight: number; // Gehäusegewicht (inkl. Tabs) [g]
    cathodeCoatingArea: number; // Beschichtungsfläche Kathode [mm²]
    anodeCoatingArea: number; // Beschichtungsfläche Anode [mm²]
    separatorCoatingArea: number; // Beschichtungsfläche Separator [mm²]
    electrolyteSwelling: number; // Elektrolytswelling [mm]
    assemblyClearance: number; // Montagefreiraum [mm]
    activeLayerCount: number; // Anzahl aktive Lagen
    capacityPerLayerC10: number | null; // Ladung pro Zelllage C/10 [Ah]
    energyPerLayerC10: number | null; // Energie pro Zelllage C/10 [Wh]
    capacityPerLayerC3: number | null; // Ladung pro Zelllage C/3 [Ah]
    energyPerLayerC3: number | null; // Energie pro Zelllage C/3 [Wh]
    separatorAreaTotal: number; // Gesamtfläche Separator[m²]
    cellLayerDelta?: number;
}

export interface CellEditorPouchMetrics extends CellEditorMetrics {
    cellLayerThicknessMax: number; // Max.Gesamtdicke Zelllagen[mm]
    cellLayerThicknessTotal: number; // Gesamtdicke Zelllagen[mm]
    cellLayerDelta: number; // Freiraum Zelllagen[μm]
    ratioCellLayerDeltaThickness: number; // Freiraum Zelllagen / Dicke Zelllage
    cathodeLayerCount: number; // Anzahl Lagen Kathode
    anodeLayerCount: number; // Anzahl Lagen Anode
    separatorLayerCount: number; // Anzahl Lagen Separator
}

export interface CellEditorPrismaMetrics extends CellEditorMetrics {
    cellLayerThicknessMax: number; // Max.Gesamtdicke Zelllagen[mm]
    cellLayerThicknessTotal: number; // Gesamtdicke Zelllagen[mm]
    cellLayerDelta: number; // Freiraum Zelllagen[μm]
    ratioCellLayerDeltaThickness: number; // Freiraum Zelllagen / Dicke Zelllage
    cathodeLayerCount: number; // Anzahl Lagen Kathode
    anodeLayerCount: number; // Anzahl Lagen Anode
    separatorLayerCount: number; // Anzahl Lagen Separator
}

export interface CellEditorCylinderMetrics extends CellEditorMetrics {
    cathodeCoatingLength: number; // Beschichtungslänge Kathode[mm]
    anodeCoatingLength: number; // Beschichtungslänge Anode[mm]
    separatorCoatingLength: number; // Beschichtungslänge Separator[mm]
    cellLayerDiameterMax: number; // Max. Gesamtdurchmesser Zelllagen[mm]
    cellLayerDiameterTotal: number; // Gesamtdurchmesser Zelllagen[mm]
    cathodeLengthTotal: number; // Gesamtlänge Kathode[mm]
    anodeLengthTotal: number; // Gesamtlänge Anode[mm]
    cathodeWindingCount: number; // Anzahl Windungen Kathode
    anodeWindingCount: number; // Anzahl Windungen Anode
    separatorWindingCount: number; // Anzahl Windungen Separator
}
export interface CellSwellingMetrics {
    cf3AbsolutBreathingWithCompression: number;
    cf3BreathingWithCompression: number;
    cf3UncompressedBreathing: number;
    freeSpaceAfterFormation: number;
    swellingBuffer: number;
    stackBreathing: number;
    totalBreathingPerLayer: number;
    swellingConstantA: number;
    swellingConstantB: number;
}

@Injectable({
    providedIn: 'root',
})
export class CellDesignMetricsService {
    public constructor(private _config: ConfigService, private _http: HttpClient) {}

    public calculateMetrics(cellDesign: CellDesign): Observable<CellDesignMetrics> {
        return this._http.post<CellDesignMetrics>(`${this._config.data.baseUrl}/api/cell-design-metrics/`, cellDesign);
    }
}
