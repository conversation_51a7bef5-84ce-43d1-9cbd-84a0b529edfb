export enum ExtrapolateModeEnum {
    clamp,
    extrapolate,
    null,
}

export function interpolate<T>(
    data: T[],
    x: number,
    selector: (item: T) => { x: number; y: number },
    extrapolate: ExtrapolateModeEnum = ExtrapolateModeEnum.clamp
): number | null {
    if (data.length === 0) {
        return null;
    } else if (data.length === 1) {
        return selector(data[0]).y;
    }

    const increasing = selector(data[0]).x <= selector(data[data.length - 1]).x;
    const index = increasing
        ? data.findIndex((item) => selector(item).x >= x)
        : data.findIndex((item) => selector(item).x <= x);

    if (index === 0) {
        // x < first(data.x)
        // extrapolate before first datapoints
        switch (extrapolate) {
            case ExtrapolateModeEnum.clamp:
            default:
                return selector(data[0]).y;

            case ExtrapolateModeEnum.extrapolate:
                const { x: prevX, y: prevY } = selector(data[0]);
                const { x: nextX, y: nextY } = selector(data[1]);

                return prevY + ((x - prevX) / (nextX - prevX)) * (nextY - prevY);

            case ExtrapolateModeEnum.null:
                return null;
        }
    } else if (index === -1) {
        // x > last(data.x)
        // extrapolate after last datapoints
        switch (extrapolate) {
            case ExtrapolateModeEnum.clamp:
            default:
                return selector(data[data.length - 1]).y;

            case ExtrapolateModeEnum.extrapolate:
                const { x: prevX, y: prevY } = selector(data[data.length - 2]);
                const { x: nextX, y: nextY } = selector(data[data.length - 1]);

                return prevY + ((x - prevX) / (nextX - prevX)) * (nextY - prevY);

            case ExtrapolateModeEnum.null:
                return null;
        }
    } else {
        const { x: prevX, y: prevY } = selector(data[index - 1]);
        const { x: nextX, y: nextY } = selector(data[index]);

        return prevY + ((x - prevX) / (nextX - prevX)) * (nextY - prevY);
    }
}
