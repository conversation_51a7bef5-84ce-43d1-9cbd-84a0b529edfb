import { MenuItem } from 'primeng/api';

const LOCALE_STORAGE_KEY = 'app-locale';

const AVAILABLE_LOCALES = ['de-DE', 'en-GB'] as const;

export type Locale = (typeof AVAILABLE_LOCALES)[number];

export function getLocale(): Locale {
    const localeValue = localStorage.getItem(LOCALE_STORAGE_KEY) ?? sessionStorage.getItem(LOCALE_STORAGE_KEY);

    const locale =
        localeValue ??
        AVAILABLE_LOCALES.find((lang) => navigator.language.startsWith(lang.slice(0, 2))) ??
        AVAILABLE_LOCALES[0];

    return locale as Locale;
}

export function setLocale(locale: Locale, storage: Storage): void {
    storage.setItem(LOCALE_STORAGE_KEY, locale);
    window.document.documentElement.lang = locale;
}

// Module types vary, and we don't particularly care about the specific one
// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
export function importLocale(locale: Locale) {
    let importLocalePromise;
    switch (locale) {
        case 'en-GB':
            importLocalePromise = import('@angular/common/locales/en-GB');
            break;
        default:
            importLocalePromise = import('@angular/common/locales/de');
            break;
    }

    return importLocalePromise;
}

function createLanguageMenuItem(label: string, selectedLocale: Locale, locale: Locale): MenuItem {
    return {
        label: label,
        command: (): void => {
            setLocale(locale, localStorage);
            location.reload();
        },
        style: selectedLocale === locale ? { backgroundColor: '#304562' } : { backgroundColor: 'inherit' },
    };
}

export function getLanguageMenuItems(): MenuItem[] {
    const selectedLocale = getLocale();

    return [
        {
            label: 'Languages:',
            items: [
                createLanguageMenuItem('Deutsch', selectedLocale, AVAILABLE_LOCALES[0]),
                createLanguageMenuItem('English', selectedLocale, AVAILABLE_LOCALES[1]),
            ],
        },
    ];
}
