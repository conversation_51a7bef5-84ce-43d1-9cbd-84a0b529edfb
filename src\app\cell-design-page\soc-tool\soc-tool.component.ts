import { Component, Input, OnInit } from '@angular/core';

import { BalancingMetrics, MaterialSummaryTableRow } from '@com/services/cell-design-metrics.service';

import { deepCloneDto } from '@com/utils/object';
import { LINE_CHART_OPTIONS } from '@com/const';
import { SoCModeEnum, SoCToolCalculatedData, SoCToolData } from '@com/utils/soc-tool';

export interface SocMetricsData {
    balancing: BalancingMetrics;
    cathodeDensity: number;
    anodeDensity: number;
}

const CONTROL_NAMES = {
    socInput: 'soc_input',
    voltageInput: 'voltage_input',
} as const;

@Component({
    selector: 'com-soc-tool[balancingMetrics][cathodeDensity][anodeDensity]',
    templateUrl: './soc-tool.component.html',
})
export class SocToolComponent implements OnInit {
    @Input()
    public set balancingMetrics(value: BalancingMetrics | undefined) {
        this._balancingMetrics = value;

        this.createSocToolDataObject();
        this.calculateData();
    }

    public get balancingMetrics(): BalancingMetrics | undefined {
        return this._balancingMetrics;
    }

    @Input()
    public set cathodeDensity(value: number | undefined) {
        this._cathodeDensity = value;

        this.createSocToolDataObject();
        this.calculateData();
    }

    public get cathodeDensity(): number | undefined {
        return this._cathodeDensity;
    }

    @Input()
    public set anodeDensity(value: number | undefined) {
        this._anodeDensity = value;

        this.createSocToolDataObject();
        this.calculateData();
    }

    public get anodeDensity(): number | undefined {
        return this._anodeDensity;
    }

    public readonly controlNames = CONTROL_NAMES;

    // chart.js have the chart options defined as any
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public options: any = {
        ...deepCloneDto(LINE_CHART_OPTIONS.rootOptions),
        plugins: deepCloneDto(LINE_CHART_OPTIONS.plugins),
        scales: deepCloneDto(LINE_CHART_OPTIONS.scales),
    };

    public isDischargeMode: boolean = false;

    public soc: number = 50;
    public voltage: number | undefined;

    public tableHeaders: string[] = ['', $localize`:@@tab.cathode:Kathode`, $localize`:@@tab.anode:Anode`];
    public tableCells: MaterialSummaryTableRow[] = [];

    public socToolData: SoCToolData | null = null;

    public socToolCalculatedData: SoCToolCalculatedData | null = null;

    private _balancingMetrics: BalancingMetrics | undefined;
    private _cathodeDensity: number | undefined;
    private _anodeDensity: number | undefined;

    public ngOnInit(): void {
        this.createDefaultOptions();
    }

    public onSocValueChange(): void {
        this.calculateData();
    }

    public onVoltageChange(): void {
        if (this.socToolData && this.voltage) {
            this.soc = this.socToolData.getSoCValue(this.voltage);
        }

        this.onSocValueChange();
    }

    public onSocModeChange(): void {
        this.createSocToolDataObject();
        this.calculateData();
    }

    private createDefaultOptions(): void {
        this.options.scales.x.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);
        this.options.scales.x.title.text = $localize`:@@axisTitle.capacityCathode: Capacity / mAh/g(CAM)`;

        this.options.scales.y.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);
        this.options.scales.y.title.text = $localize`:@@axisTitle.cellPotential: Cell potential / V`;
    }

    private createSocToolDataObject(): void {
        if (!this.balancingMetrics?.chart || !this.anodeDensity || !this.cathodeDensity) {
            return;
        }

        this.socToolData = new SoCToolData(
            this.balancingMetrics.chart,
            this.anodeDensity,
            this.cathodeDensity,
            this.isDischargeMode ? SoCModeEnum.discharging : SoCModeEnum.charging
        );
    }

    private calculateData(): void {
        if (!this.socToolData) {
            return;
        }

        this.socToolCalculatedData = this.socToolData.calculateSoCToolData(this.soc);

        if (this.socToolCalculatedData) {
            this.voltage = this.socToolCalculatedData.cell.voltage;

            this.options.plugins['annotation'] = {
                annotations: this.socToolCalculatedData.chartAnnotations,
            };

            this.tableCells = [
                {
                    cells: [
                        {
                            value: $localize`:@@common.voltageLithium:Spannung vs. Li`,
                        },
                        {
                            value: this.socToolCalculatedData.cathode.blend.voltage,
                            unit: 'V',
                        },
                        {
                            value: this.socToolCalculatedData.anode.blend.voltage,
                            unit: 'V',
                        },
                    ],
                },
                {
                    cells: [
                        { value: $localize`:@@common.soc:SoC` },
                        { value: this.socToolCalculatedData.cathode.blend.soc },
                        { value: this.socToolCalculatedData.anode.blend.soc },
                    ],
                },
                {
                    cells: [
                        { value: 'c' },
                        {
                            value: this.socToolCalculatedData.cathode.blend.concentration ?? 'NaN',
                            unit: 'mol/l',
                        },
                        {
                            value: this.socToolCalculatedData.anode.blend.concentration ?? 'NaN',
                            unit: 'mol/l',
                        },
                    ],
                },
                {
                    cells: [
                        { value: $localize`:@@common.socMaterial:SoC Material ${'1'}:INTERPOLATION:` },
                        { value: this.socToolCalculatedData.cathode.material1?.soc ?? 'NaN' },
                        { value: this.socToolCalculatedData.anode.material1?.soc ?? 'NaN' },
                    ],
                },
                {
                    cells: [
                        { value: 'c1(Li)' },
                        {
                            value: this.socToolCalculatedData.cathode.material1?.concentration ?? 'NaN',
                            unit: 'mol/l',
                        },
                        {
                            value: this.socToolCalculatedData.anode.material1?.concentration ?? 'NaN',
                            unit: 'mol/l',
                        },
                    ],
                },
                {
                    cells: [
                        { value: $localize`:@@common.socMaterial:SoC Material ${'2'}:INTERPOLATION:` },
                        { value: this.socToolCalculatedData.cathode.material2?.soc ?? 'NaN' },
                        { value: this.socToolCalculatedData.anode.material2?.soc ?? 'NaN' },
                    ],
                },
                {
                    cells: [
                        { value: 'c2(Li)' },
                        {
                            value: this.socToolCalculatedData.cathode.material2?.concentration ?? 'NaN',
                            unit: 'mol/l',
                        },
                        {
                            value: this.socToolCalculatedData.anode.material2?.concentration ?? 'NaN',
                            unit: 'mol/l',
                        },
                    ],
                },
            ];
        }
    }
}
